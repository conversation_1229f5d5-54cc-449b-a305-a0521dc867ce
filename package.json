{"name": "flex-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "vite --open", "dev:backend": "convex dev", "lint": "tsc -p convex -noEmit --pretty false && tsc -p . -noEmit --pretty false && convex dev --once && vite build", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:backend": "vitest run backend/", "test:frontend": "vitest run src/", "test:integration": "playwright test", "test:integration:collaboration": "playwright test collaboration.integration.test.ts", "test:integration:sharing": "playwright test document-sharing.integration.test.ts", "test:integration:access": "playwright test access-requests.integration.test.ts", "test:integration:permissions": "playwright test permissions.integration.test.ts", "test:component": "playwright test --config=playwright-ct.config.ts", "test:all": "npm run test:backend && npm run test:frontend && npm run test:integration", "test:collaboration": "npm run test:integration:collaboration", "test:permissions": "npm run test:backend && npm run test:integration:permissions"}, "dependencies": {"@blocknote/core": "^0.32.0-hackdays.0", "@blocknote/mantine": "^0.32.0-hackdays.0", "@blocknote/react": "^0.32.0-hackdays.0", "@convex-dev/auth": "^0.0.80", "@convex-dev/presence": "^0.1.0", "@convex-dev/prosemirror-sync": "^0.1.22", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.24.2", "date-fns": "^4.1.0", "lucide-react": "^0.513.0", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.40.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.1.1", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@playwright/experimental-ct-react": "^1.49.1", "@playwright/test": "^1.49.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.13.10", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "autoprefixer": "~10", "dotenv": "^16.4.7", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "jsdom": "^25.0.1", "npm-run-all": "^4.1.5", "postcss": "~8", "prettier": "^3.5.3", "tailwindcss": "~3", "tailwindcss-animate": "^1.0.7", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0", "vitest": "^2.1.9"}}
// Test script to verify permission-based access control
// This script can be run in the browser console to test different scenarios

console.log("🧪 Testing Permission-Based Access Control for Document Creation");

// Test helper function to simulate different user states
async function testPermissionScenario(scenarioName, userState) {
  console.log(`\n📋 Testing: ${scenarioName}`);
  console.log(`User State:`, userState);
  
  // This would need to be adapted to work with the actual Convex client
  // For now, this is a conceptual test structure
  
  try {
    // Simulate the permission check logic
    let result;
    
    if (!userState.userId) {
      result = { canCreate: false, reason: "not_authenticated" };
    } else if (!userState.user) {
      result = { canCreate: false, reason: "user_not_found" };
    } else if (userState.user.isAnonymous) {
      result = { canCreate: false, reason: "anonymous_user" };
    } else if (userState.ownedDocuments > 0) {
      result = { canCreate: true, reason: "document_owner" };
    } else if (userState.writeShares > 0) {
      result = { canCreate: true, reason: "has_write_permission" };
    } else if (userState.anyShares > 0) {
      result = { canCreate: true, reason: "has_shared_access" };
    } else {
      result = { canCreate: true, reason: "authenticated_user" };
    }
    
    console.log(`✅ Result:`, result);
    
    // Test expected behavior
    const expectedMessages = {
      "anonymous_user": "Anonymous users cannot create documents. Please sign up for an account.",
      "not_authenticated": "Please sign in to create documents.",
      "user_not_found": "You don't have permission to create documents."
    };
    
    if (!result.canCreate && expectedMessages[result.reason]) {
      console.log(`📝 Expected Message: "${expectedMessages[result.reason]}"`);
    }
    
    return result;
    
  } catch (error) {
    console.error(`❌ Error in ${scenarioName}:`, error);
    return null;
  }
}

// Test scenarios
async function runAllTests() {
  console.log("🚀 Starting permission tests...\n");
  
  // Scenario 1: Anonymous User
  await testPermissionScenario("Anonymous User", {
    userId: "user123",
    user: { isAnonymous: true, name: null, email: null },
    ownedDocuments: 0,
    writeShares: 0,
    anyShares: 0
  });
  
  // Scenario 2: Unauthenticated User
  await testPermissionScenario("Unauthenticated User", {
    userId: null,
    user: null,
    ownedDocuments: 0,
    writeShares: 0,
    anyShares: 0
  });
  
  // Scenario 3: New Authenticated User (no documents or shares)
  await testPermissionScenario("New Authenticated User", {
    userId: "user123",
    user: { isAnonymous: false, name: "John Doe", email: "<EMAIL>" },
    ownedDocuments: 0,
    writeShares: 0,
    anyShares: 0
  });
  
  // Scenario 4: Document Owner
  await testPermissionScenario("Document Owner", {
    userId: "user123",
    user: { isAnonymous: false, name: "John Doe", email: "<EMAIL>" },
    ownedDocuments: 2,
    writeShares: 0,
    anyShares: 0
  });
  
  // Scenario 5: User with Write Permission
  await testPermissionScenario("User with Write Permission", {
    userId: "user123",
    user: { isAnonymous: false, name: "John Doe", email: "<EMAIL>" },
    ownedDocuments: 0,
    writeShares: 1,
    anyShares: 1
  });
  
  // Scenario 6: User with Read-only Permission
  await testPermissionScenario("User with Read-only Permission", {
    userId: "user123",
    user: { isAnonymous: false, name: "John Doe", email: "<EMAIL>" },
    ownedDocuments: 0,
    writeShares: 0,
    anyShares: 1
  });
  
  console.log("\n✨ All permission tests completed!");
  console.log("\n📊 Summary:");
  console.log("- Anonymous users: ❌ Cannot create documents");
  console.log("- Unauthenticated users: ❌ Cannot create documents");
  console.log("- New authenticated users: ✅ Can create documents");
  console.log("- Document owners: ✅ Can create documents");
  console.log("- Users with write permission: ✅ Can create documents");
  console.log("- Users with read-only permission: ✅ Can create documents");
}

// Instructions for manual testing
console.log(`
🔧 Manual Testing Instructions:

1. Test Anonymous User:
   - Click "Sign in anonymously" 
   - Check that "New Document" button is disabled
   - Verify warning message appears

2. Test Unauthenticated User:
   - Sign out completely
   - Verify you see the sign-in form
   - No document creation options should be visible

3. Test New Authenticated User:
   - Sign up with a new email/password
   - Check that "New Document" button is enabled
   - Try creating a document

4. Test Document Owner:
   - Create a document as an authenticated user
   - Verify "New Document" button remains enabled
   - Check that you can create additional documents

5. Test User with Shared Access:
   - Have another user share a document with you
   - Verify "New Document" button is enabled
   - Try creating your own document

To run the conceptual tests, call: runAllTests()
`);

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.testPermissions = runAllTests;
  window.testScenario = testPermissionScenario;
}

# Permission-Based Access Control Test Plan

## Implementation Summary

The document creation permission system has been updated to implement proper access control based on user permissions:

### Users who CAN create documents:
1. **Document owners** - Users who have created at least one document
2. **Users with write permission** - Users who have been granted write access to any document
3. **Users with shared access** - Users who have been shared any document (read or write)
4. **New authenticated users** - To allow new users to get started

### Users who CANNOT create documents:
1. **Anonymous users** - Users who haven't signed up for an account
2. **Unauthenticated users** - Users who aren't signed in

## Changes Made

### Backend Changes (`convex/documents.ts`):

1. **Updated `canCreateDocuments` query**:
   - Now returns an object with `canCreate` boolean and `reason` string
   - Implements comprehensive permission checking logic
   - Provides specific reasons for denial

2. **Updated `createDocument` mutation**:
   - Now uses the same permission logic as the query
   - Provides specific error messages based on the denial reason
   - Ensures backend validation aligns with frontend checks

### Frontend Changes (`src/components/DocumentList.tsx`):

1. **Enhanced permission handling**:
   - Updated to handle the new permission result structure
   - Maintains backward compatibility with boolean checks
   - Provides specific messaging based on denial reasons

2. **Improved user messaging**:
   - Different messages for anonymous users vs. authenticated users without permissions
   - More helpful guidance in empty states
   - Consistent messaging across all UI elements

## Test Scenarios

### Scenario 1: Anonymous User
- **Expected**: Cannot create documents
- **Message**: "Anonymous users cannot create documents. Please sign up for an account."
- **UI**: Button disabled, warning card shown

### Scenario 2: New Authenticated User
- **Expected**: Can create documents
- **Reason**: Allows new users to get started
- **UI**: Button enabled, no warning

### Scenario 3: User with Read Permission
- **Expected**: Can create documents
- **Reason**: Users trusted with document access can create their own
- **UI**: Button enabled, no warning

### Scenario 4: User with Write Permission
- **Expected**: Can create documents
- **Reason**: Write permission indicates trusted user
- **UI**: Button enabled, no warning

### Scenario 5: Document Owner
- **Expected**: Can create documents
- **Reason**: Owners have full creation privileges
- **UI**: Button enabled, no warning

## Security Considerations

1. **Backend Validation**: All permission checks are enforced on the backend
2. **Consistent Logic**: Frontend and backend use the same permission logic
3. **Graceful Degradation**: UI provides helpful feedback for denied access
4. **Future-Proof**: Permission system can be easily extended

## Notes

- The implementation allows new authenticated users to create documents to ensure a good onboarding experience
- Users with any level of document sharing are considered trusted enough to create documents
- Anonymous users are completely blocked from document creation
- All error messages are user-friendly and provide clear next steps

# Testing Guide for Collaborative Content Editor

This document provides comprehensive instructions for running and understanding the test suite for the collaborative content editor application.

## Overview

The test suite covers three main areas using modern testing tools:
- **Backend Unit Tests**: Permission-based access control system (Vitest)
- **Frontend Component Tests**: UI components and user interactions (Vitest + React Testing Library)
- **Integration Tests**: End-to-end workflows and browser automation (Playwright)

## Test Structure

```
├── backend/tests/
│   └── permissions.test.ts          # Backend permission system tests (Vitest)
├── src/components/__tests__/
│   ├── DocumentList.vitest.test.tsx # Component tests (Vitest)
│   └── DocumentList.playwright.test.tsx # Component tests (Playwright)
├── src/__tests__/integration/
│   └── document-creation.playwright.test.ts # Integration tests (Playwright)
├── src/setupTests.ts                # Test configuration and mocks
├── vitest.config.ts                 # Vitest configuration
├── playwright.config.ts             # Playwright configuration
├── playwright-ct.config.ts          # Playwright component testing config
└── README-TESTING.md               # This file
```

## Prerequisites

Ensure you have the following installed:
- Node.js (v18 or higher)
- npm or yarn package manager

## Installation

Install test dependencies:

```bash
npm install
```

## Running Tests

### All Tests (Vitest + Playwright)
```bash
npm run test:all
```

### Backend Tests Only (Vitest)
```bash
npm run test:backend
```

### Frontend Component Tests (Vitest)
```bash
npm run test:frontend
```

### Integration Tests (Playwright)
```bash
npm run test:integration
```

### Component Tests (Playwright)
```bash
npm run test:component
```

### Watch Mode (for development)
```bash
npm run test:watch
```

### Coverage Report (Vitest)
```bash
npm run test:coverage
```

## Test Categories

### 1. Backend Unit Tests (`backend/tests/permissions.test.ts`) - Vitest

Tests the core permission logic for document creation using Vitest:

**Scenarios Covered:**
- ✅ Unauthenticated users (denied access)
- ✅ Anonymous users (denied access)
- ✅ New authenticated users (allowed access)
- ✅ Document owners (allowed access)
- ✅ Users with write permissions (allowed access)
- ✅ Users with read-only permissions (allowed access)
- ✅ Error handling for invalid user states

**Key Functions Tested:**
- `canCreateDocuments` query
- `createDocument` mutation
- Permission validation logic
- Error message generation

### 2. Frontend Component Tests (`src/components/__tests__/`) - Vitest + Playwright

Tests the DocumentList component UI and interactions using both Vitest and Playwright:

**Vitest Tests (`DocumentList.vitest.test.tsx`):**
- ✅ Loading states and spinners
- ✅ Permission-based UI rendering
- ✅ Document list display with correct permissions
- ✅ Create/delete button visibility
- ✅ Empty states for different user types
- ✅ Document creation dialog workflow
- ✅ Error handling and toast notifications

**Playwright Component Tests (`DocumentList.playwright.test.tsx`):**
- ✅ Browser-based component rendering
- ✅ Real user interactions
- ✅ Cross-browser compatibility
- ✅ Visual regression testing capabilities

### 3. Integration Tests (`src/__tests__/integration/`) - Playwright

Tests complete end-to-end workflows using Playwright browser automation:

**Scenarios Covered:**
- ✅ Full document creation workflow
- ✅ Permission-based UI behavior
- ✅ Error handling and user feedback
- ✅ Cross-browser compatibility (Chrome, Firefox, Safari)
- ✅ Responsive design testing
- ✅ Real network interactions
- ✅ Loading states during operations

## Permission Test Scenarios

Based on `test-permissions.md`, the following scenarios are thoroughly tested:

### Scenario 1: Anonymous User
- **Expected**: Cannot create documents
- **UI**: Button disabled, warning card shown
- **Message**: "Anonymous users cannot create documents. Please sign up for an account."

### Scenario 2: New Authenticated User
- **Expected**: Can create documents
- **UI**: Button enabled, no warning
- **Reason**: Allows new users to get started

### Scenario 3: User with Read Permission
- **Expected**: Can create documents
- **UI**: Button enabled, no warning
- **Reason**: Users trusted with document access can create their own

### Scenario 4: User with Write Permission
- **Expected**: Can create documents
- **UI**: Button enabled, no warning
- **Reason**: Write permission indicates trusted user

### Scenario 5: Document Owner
- **Expected**: Can create documents
- **UI**: Button enabled, no warning
- **Reason**: Owners have full creation privileges

## Mock Strategy

### Convex Mocks
- `useQuery` and `useMutation` hooks are mocked
- Database operations are simulated
- Authentication states are controlled

### Component Mocks
- Toast notifications are mocked
- Icon components are replaced with test-friendly versions
- External dependencies are isolated

### Test Data
- Realistic user and document objects
- Various permission combinations
- Error scenarios and edge cases

## Coverage Goals

The test suite aims for:
- **80%+ code coverage** for permission-related code
- **100% scenario coverage** for all permission combinations
- **Complete error path testing** for user feedback
- **UI state testing** for all permission levels

## Debugging Tests

### Verbose Output
```bash
npm test -- --verbose
```

### Run Specific Test File
```bash
npm test -- DocumentList.test.jsx
```

### Run Specific Test Case
```bash
npm test -- --testNamePattern="should allow access for document owners"
```

### Debug Mode
```bash
npm test -- --detectOpenHandles --forceExit
```

## Common Issues and Solutions

### 1. React 19 Compatibility
If you encounter peer dependency warnings:
```bash
npm install --legacy-peer-deps
```

### 2. Mock Issues
If mocks aren't working properly, check:
- `src/setupTests.ts` is properly configured
- Jest configuration includes the setup file
- Mock implementations match the actual API

### 3. Async Test Issues
For async operations, ensure you're using:
- `await waitFor()` for DOM updates
- `await user.click()` for user interactions
- Proper promise handling in mocks

## CI/CD Integration

The test suite is designed to work with CI/CD pipelines:

```yaml
# Example GitHub Actions step
- name: Run Tests
  run: |
    npm ci
    npm run test:coverage
    
- name: Upload Coverage
  uses: codecov/codecov-action@v3
  with:
    file: ./coverage/lcov.info
```

## Contributing

When adding new tests:

1. **Follow the existing patterns** for mocking and setup
2. **Test both positive and negative scenarios**
3. **Include error handling tests**
4. **Update this README** if adding new test categories
5. **Ensure tests are deterministic** and don't rely on external state

## Performance Considerations

- Tests run in parallel by default
- Mocks prevent actual network calls
- Test timeout is set to 10 seconds
- Coverage collection may slow down test execution

## Troubleshooting

### Tests Failing Locally
1. Clear Jest cache: `npx jest --clearCache`
2. Reinstall dependencies: `rm -rf node_modules && npm install`
3. Check for version conflicts in package.json

### Coverage Issues
1. Ensure all test files are properly named (*.test.js, *.spec.js)
2. Check Jest configuration for correct file patterns
3. Verify mock implementations don't interfere with coverage

For additional help, refer to the main project documentation or create an issue in the repository.

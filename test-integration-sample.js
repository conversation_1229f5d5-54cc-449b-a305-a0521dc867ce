#!/usr/bin/env node

/**
 * Simple test runner to verify integration tests are working
 * This script runs a single test to validate the setup
 */

const { execSync } = require('child_process');

console.log('🧪 Testing Integration Test Setup...\n');

try {
  // Run a single test from the collaboration suite
  console.log('Running collaboration integration test...');
  
  const result = execSync(
    'npx playwright test collaboration.integration.test.ts --grep "should support multiple users editing the same document simultaneously" --project=chromium',
    { 
      encoding: 'utf8',
      cwd: process.cwd(),
      timeout: 60000
    }
  );
  
  console.log('✅ Test completed successfully!');
  console.log('\nTest output:');
  console.log(result);
  
} catch (error) {
  console.log('❌ Test failed or encountered issues:');
  console.log(error.stdout || error.message);
  
  if (error.stderr) {
    console.log('\nError details:');
    console.log(error.stderr);
  }
  
  console.log('\n📝 This is expected if the development server is not running.');
  console.log('To run the full test suite:');
  console.log('1. Start the development server: npm run dev');
  console.log('2. In another terminal, run: npm run test:integration');
}

console.log('\n📋 Available test commands:');
console.log('- npm run test:integration:collaboration  # Real-time collaboration tests');
console.log('- npm run test:integration:sharing        # Document sharing tests');
console.log('- npm run test:integration:access         # Access request tests');
console.log('- npm run test:integration:permissions    # Permission boundary tests');
console.log('- npm run test:integration                # All integration tests');
console.log('- npm run test:all                        # Complete test suite');

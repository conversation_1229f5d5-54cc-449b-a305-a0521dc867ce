<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test ScreenplayEditor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .instructions {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ScreenplayEditor Test Page</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li>Open the main application at <a href="http://localhost:5175" target="_blank">http://localhost:5175</a></li>
                <li>Create a new script using the "New Script" button</li>
                <li>Add a screenplay document using "Add Document" → "Screenplay - Industry format"</li>
                <li>Navigate to the screenplay document</li>
                <li>Check if the ScreenplayEditor loads without the "Editor Failed to Load" error</li>
                <li>Open browser console (F12) and check for JavaScript errors</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>Test 1: Basic Component Loading</h3>
            <p>Check if the ScreenplayEditor component loads without crashing.</p>
            <div class="status warning">
                <strong>Manual Test Required:</strong> Navigate to a screenplay document and verify the editor loads.
            </div>
        </div>

        <div class="test-section">
            <h3>Test 2: Console Error Check</h3>
            <p>Check browser console for JavaScript errors related to ScreenplayEditor.</p>
            <div class="code">
// Run this in browser console to check for errors:
console.clear();
console.log('=== ScreenplayEditor Debug ===');

// Check if we're on a screenplay document page
const url = window.location.href;
console.log('Current URL:', url);

// Check for error messages
const errorElements = Array.from(document.querySelectorAll('*')).filter(el => 
  el.textContent && el.textContent.includes('Editor Failed to Load')
);
console.log('Error elements found:', errorElements.length);

// Check for ScreenplayEditor component
const screenplayEditor = document.querySelector('[data-testid="screenplay-editor"]');
console.log('ScreenplayEditor found:', !!screenplayEditor);

// Check for BlockNote editor
const blockNoteEditor = document.querySelector('.bn-editor, [data-testid="blocknote-view"]');
console.log('BlockNote editor found:', !!blockNoteEditor);

// Check for loading states
const loadingSpinner = document.querySelector('.animate-spin');
console.log('Loading spinner present:', !!loadingSpinner);
            </div>
        </div>

        <div class="test-section">
            <h3>Test 3: Component Functionality</h3>
            <p>Test basic editor functionality if it loads successfully.</p>
            <div class="status warning">
                <strong>Manual Test Required:</strong> 
                <ul>
                    <li>Try typing in the editor</li>
                    <li>Check if the screenplay toolbar appears</li>
                    <li>Verify the paginated layout is working</li>
                    <li>Test if collaborative features work (open in multiple tabs)</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>Expected Results</h3>
            <div class="status success">
                <strong>Success Criteria:</strong>
                <ul>
                    <li>No "Editor Failed to Load" error message</li>
                    <li>ScreenplayEditor component renders properly</li>
                    <li>BlockNote editor initializes within the paginated layout</li>
                    <li>Screenplay toolbar appears and functions</li>
                    <li>No JavaScript errors in console</li>
                    <li>Document header and navigation work</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>Common Issues to Check</h3>
            <div class="status error">
                <strong>Potential Problems:</strong>
                <ul>
                    <li>useBlockNoteSync hook initialization errors</li>
                    <li>Custom schema compatibility issues</li>
                    <li>CSS injection problems with screenplay styles</li>
                    <li>Hook dependency or state management issues</li>
                    <li>Component rendering or error boundary failures</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>

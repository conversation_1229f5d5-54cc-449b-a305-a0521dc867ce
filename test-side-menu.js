// Manual test script to check if side menu is working
// Run this in the browser console when on a document page

console.log('=== Side Menu Test ===');

// Test 1: Check if BlockNoteView is rendered
const blockNoteView = document.querySelector('.prose, [data-testid="blocknote-view"]');
console.log('1. BlockNoteView found:', !!blockNoteView);

// Test 2: Check for side menu related elements
const sideMenuElements = document.querySelectorAll('[class*="side"], [class*="menu"], [data-testid*="side"]');
console.log('2. Side menu elements found:', sideMenuElements.length);
sideMenuElements.forEach((el, i) => {
  console.log(`   ${i}: ${el.className} - ${el.tagName}`);
});

// Test 3: Check for plus buttons
const plusButtons = document.querySelectorAll('button:contains("+"), [aria-label*="Add"], [title*="Add"]');
console.log('3. Plus/Add buttons found:', plusButtons.length);

// Test 4: Check for drag handles
const dragHandles = document.querySelectorAll('button:contains("⠿"), [aria-label*="Drag"], [title*="Drag"]');
console.log('4. Drag handles found:', dragHandles.length);

// Test 5: Check for BlockNote specific classes
const blockNoteClasses = document.querySelectorAll('[class*="bn-"], [class*="blocknote"]');
console.log('5. BlockNote classes found:', blockNoteClasses.length);

// Test 6: Check if sideMenu prop is correctly set
const blockNoteViewElement = document.querySelector('.prose');
if (blockNoteViewElement) {
  console.log('6. BlockNoteView element found, checking for side menu...');
  
  // Look for side menu in the DOM structure
  const parent = blockNoteViewElement.closest('[data-testid="blocknote-view"]') || blockNoteViewElement.parentElement;
  if (parent) {
    const sideMenuInParent = parent.querySelectorAll('[class*="side"], [data-testid*="side"]');
    console.log('   Side menu elements in parent:', sideMenuInParent.length);
  }
}

// Test 7: Check for any BlockNote side menu specific elements
const bnSideMenu = document.querySelectorAll('.bn-side-menu, [data-bn-type="side-menu"]');
console.log('7. BlockNote side menu elements:', bnSideMenu.length);

// Test 8: Look for any buttons that might be the side menu tools
const allButtons = document.querySelectorAll('button');
console.log('8. All buttons found:', allButtons.length);
const relevantButtons = Array.from(allButtons).filter(btn => {
  const text = btn.textContent || '';
  const ariaLabel = btn.getAttribute('aria-label') || '';
  const title = btn.getAttribute('title') || '';
  return text.includes('+') || text.includes('⠿') || 
         ariaLabel.toLowerCase().includes('add') || 
         ariaLabel.toLowerCase().includes('drag') ||
         title.toLowerCase().includes('add') ||
         title.toLowerCase().includes('drag');
});
console.log('   Relevant buttons:', relevantButtons.length);
relevantButtons.forEach((btn, i) => {
  console.log(`   ${i}: "${btn.textContent}" - aria-label: "${btn.getAttribute('aria-label')}" - title: "${btn.getAttribute('title')}"`);
});

console.log('=== End Side Menu Test ===');

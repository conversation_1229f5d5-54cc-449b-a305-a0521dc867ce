import { vi, beforeAll, afterAll } from 'vitest';
import * as React from 'react';

// Add custom matchers for DOM testing
import '@testing-library/jest-dom';

// Mock Convex client
const mockConvexClient = {
  query: vi.fn(),
  mutation: vi.fn(),
  action: vi.fn(),
};

// Mock Convex React hooks
vi.mock('convex/react', () => ({
  useQuery: vi.fn(),
  useMutation: vi.fn(),
  useAction: vi.fn(),
  ConvexProvider: ({ children }: { children: React.ReactNode }) => children,
  ConvexReactClient: vi.fn(() => mockConvexClient),
}));

// Mock Convex auth
vi.mock('@convex-dev/auth/react', () => ({
  useAuthActions: vi.fn(() => ({
    signIn: vi.fn(),
    signOut: vi.fn(),
  })),
  Authenticated: ({ children }: { children: React.ReactNode }) => children,
  Unauthenticated: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock toast hook
vi.mock('./hooks/use-toast', () => ({
  useToast: vi.fn(() => ({
    toast: vi.fn(),
  })),
}));

// Mock Lucide React icons
vi.mock('lucide-react', () => ({
  Plus: () => React.createElement('div', { 'data-testid': 'plus-icon' }),
  FileText: () => React.createElement('div', { 'data-testid': 'file-text-icon' }),
  Trash2: () => React.createElement('div', { 'data-testid': 'trash-icon' }),
  Crown: () => React.createElement('div', { 'data-testid': 'crown-icon' }),
  Eye: () => React.createElement('div', { 'data-testid': 'eye-icon' }),
  Edit: () => React.createElement('div', { 'data-testid': 'edit-icon' }),
  Lock: () => React.createElement('div', { 'data-testid': 'lock-icon' }),
  AlertCircle: () => React.createElement('div', { 'data-testid': 'alert-circle-icon' }),
  X: () => React.createElement('div', { 'data-testid': 'x-icon' }),
  Loader2: () => React.createElement('div', { 'data-testid': 'loader-icon' }),
  Check: () => React.createElement('div', { 'data-testid': 'check-icon' }),
  ChevronDown: () => React.createElement('div', { 'data-testid': 'chevron-down-icon' }),
  Search: () => React.createElement('div', { 'data-testid': 'search-icon' }),
  Settings: () => React.createElement('div', { 'data-testid': 'settings-icon' }),
  User: () => React.createElement('div', { 'data-testid': 'user-icon' }),
  LogOut: () => React.createElement('div', { 'data-testid': 'logout-icon' }),
}));

// Global test utilities
(global as any).mockConvexClient = mockConvexClient;

// Suppress console warnings in tests
const originalConsoleWarn = console.warn;
beforeAll(() => {
  console.warn = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('React does not recognize')
    ) {
      return;
    }
    originalConsoleWarn(...args);
  };
});

afterAll(() => {
  console.warn = originalConsoleWarn;
});

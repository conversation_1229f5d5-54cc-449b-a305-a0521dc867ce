import usePresence from "@convex-dev/presence/react";
import { api } from "../../convex/_generated/api";
import { useQuery } from "convex/react";
import { getUserColor } from "../lib/userColors";

interface PresenceIndicatorProps {
  roomId: string;
}

export function PresenceIndicator({ roomId }: PresenceIndicatorProps) {
  const userId = useQuery(api.presence.getUserId);
  const presenceState = usePresence(api.presence, roomId, userId || "");

  if (!userId || !presenceState || presenceState.length === 0) {
    return null;
  }

  return (
    <div className="flex items-center gap-2 p-3 bg-white border-b border-gray-200">
      <span className="text-sm text-gray-600 font-medium">
        {presenceState.length === 1 ? "1 person" : `${presenceState.length} people`} editing
      </span>
      <div className="flex -space-x-2">
        {presenceState.slice(0, 8).map((user, index) => {
          const userColor = getUserColor(user.userId);
          return (
            <div
              key={`${user.userId}-${index}`}
              className={`w-8 h-8 rounded-full border-2 border-white flex items-center justify-center ${userColor.text} text-xs font-semibold ${userColor.bg} shadow-sm`}
              title={user.name || "Anonymous"}
            >
              {user.image ? (
                <img
                  src={user.image}
                  alt={user.name || "User"}
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                <span>
                  {(user.name || "A").charAt(0).toUpperCase()}
                </span>
              )}
            </div>
          );
        })}
        {presenceState.length > 8 && (
          <div className="w-8 h-8 rounded-full border-2 border-white bg-gray-500 flex items-center justify-center text-white text-xs font-semibold shadow-sm">
            +{presenceState.length - 8}
          </div>
        )}
      </div>
    </div>
  );
}

import { useState } from "react";
import * as React from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Button } from "./ui/button";
import { Card, CardContent } from "./ui/card";
import { Badge } from "./ui/badge";
import { Input } from "./ui/input";
import {
  Plus,
  FileText,
  Trash2,
  Crown,
  Eye,
  Edit,
  Lock,
  AlertCircle
} from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "./ui/dialog";
import { useToast } from "../hooks/use-toast";
import { useDocumentUrls } from "../hooks/useDocumentRouting";
import { Id } from "../../convex/_generated/dataModel";

interface DocumentListProps {
  onSelectDocument: (documentId: Id<"documents">) => void;
  selectedDocumentId?: Id<"documents"> | null;
  onDocumentCountChange?: (count: number) => void;
}

export function DocumentList({ onSelectDocument, selectedDocumentId, onDocumentCountChange }: DocumentListProps) {
  const documents = useQuery(api.documents.getUserDocuments);
  const canCreateResult = useQuery(api.documents.canCreateDocuments);
  const createDocument = useMutation(api.documents.createDocument);
  const deleteDocument = useMutation(api.documents.deleteDocument);
  const { getDocumentUrl } = useDocumentUrls();

  // Extract canCreate boolean for backward compatibility
  const canCreate = canCreateResult?.canCreate ?? false;

  // Notify parent component of document count changes
  React.useEffect(() => {
    if (documents && onDocumentCountChange) {
      onDocumentCountChange(documents.length);
    }
  }, [documents, onDocumentCountChange]);

  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // State for create document dialog
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newDocumentTitle, setNewDocumentTitle] = useState("");
  const [titleError, setTitleError] = useState("");

  const { toast } = useToast();

  // Validate document title
  const validateTitle = (title: string): string => {
    if (!title.trim()) {
      return "Document name cannot be empty";
    }
    if (title.trim().length > 100) {
      return "Document name must be 100 characters or less";
    }
    // Check for invalid characters (basic validation)
    const invalidChars = /[<>:"/\\|?*]/;
    if (invalidChars.test(title)) {
      return "Document name contains invalid characters";
    }
    return "";
  };

  // Show create document dialog
  const handleShowCreateDialog = () => {
    if (!canCreate) {
      const reason = canCreateResult?.reason || "unknown";
      let description = "You don't have permission to create documents.";

      switch (reason) {
        case "anonymous_user":
          description = "Anonymous users cannot create documents. Please sign up for an account.";
          break;
        case "not_authenticated":
          description = "Please sign in to create documents.";
          break;
        default:
          description = "You don't have permission to create documents.";
      }

      toast({
        title: "Access Denied",
        description,
        variant: "destructive",
      });
      return;
    }

    setNewDocumentTitle("");
    setTitleError("");
    setShowCreateDialog(true);
  };

  // Create document with custom title
  const handleCreateDocument = async () => {
    const error = validateTitle(newDocumentTitle);
    if (error) {
      setTitleError(error);
      return;
    }

    setIsCreating(true);
    try {
      const documentId = await createDocument({
        title: newDocumentTitle.trim(),
      });

      // Close dialog and reset form
      setShowCreateDialog(false);
      setNewDocumentTitle("");
      setTitleError("");

      // Immediately navigate to the new document
      onSelectDocument(documentId);

      toast({
        title: "Document created",
        description: `"${newDocumentTitle.trim()}" is ready to edit!`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create document",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteDocument = async () => {
    if (!documentToDelete) return;

    setIsDeleting(true);
    try {
      await deleteDocument({ id: documentToDelete as any });
      setShowDeleteDialog(false);
      setDocumentToDelete(null);
      toast({
        title: "Document deleted",
        description: "The document has been permanently deleted.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete document",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case "owner":
        return <Crown className="h-3 w-3" />;
      case "write":
        return <Edit className="h-3 w-3" />;
      case "read":
        return <Eye className="h-3 w-3" />;
      default:
        return <Lock className="h-3 w-3" />;
    }
  };

  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case "owner":
        return "default";
      case "write":
        return "secondary";
      case "read":
        return "outline";
      default:
        return "destructive";
    }
  };

  if (!documents) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-2 text-muted-foreground">Loading documents...</span>
      </div>
    );
  }

  return (
    <div className="w-80 h-screen bg-white border-r border-gray-200 flex flex-col">
      {/* Sidebar Header */}
      <div className="p-6 border-b border-gray-200">
        <h1 className="text-xl font-semibold text-gray-900 mb-4">My Documents</h1>

        {/* Create Document Button - Opens dialog for custom naming */}
        {canCreate && (
          <Button
            onClick={handleShowCreateDialog}
            disabled={isCreating}
            className="w-full gap-2 bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="h-4 w-4" />
            New Document
          </Button>
        )}
      </div>

      {/* Sidebar Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">

        {canCreate === false && (
          <Card className="border-amber-200 bg-amber-50">
            <CardContent className="p-3">
              <div className="flex items-start gap-2 text-amber-800">
                <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-sm">Limited Access</p>
                  <p className="text-xs">
                    {canCreateResult?.reason === "anonymous_user"
                      ? "Anonymous users can only view shared documents."
                      : canCreateResult?.reason === "not_authenticated"
                        ? "Please sign in to create and manage documents."
                        : "You can view shared documents but cannot create new ones."
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {documents.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <FileText className="h-8 w-8 text-muted-foreground mb-3" />
            <h3 className="text-sm font-medium mb-2">No documents yet</h3>
            <p className="text-xs text-muted-foreground mb-4">
              {canCreate === false
                ? canCreateResult?.reason === "anonymous_user"
                  ? "Sign up for an account or ask someone to share a document with you."
                  : "Ask someone to share a document with you."
                : "Create your first document to get started."
              }
            </p>
          </div>
        ) : (
          <div className="space-y-2">
            {documents.map((document) => {
              if (!document) return null;
              const isSelected = selectedDocumentId === document._id;
              const documentUrl = getDocumentUrl(document._id);

              const handleDocumentClick = (e: React.MouseEvent) => {
                e.preventDefault();
                onSelectDocument(document._id);
              };

              return (
                <a
                  key={document._id}
                  href={documentUrl}
                  className={`group p-3 rounded-lg border cursor-pointer transition-all relative block no-underline ${isSelected
                    ? 'border-blue-500 bg-blue-50 shadow-sm'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  onClick={handleDocumentClick}
                >
                  {/* Left border indicator for selected state */}
                  {isSelected && (
                    <div className="absolute left-0 top-0 bottom-0 w-1 bg-blue-500 rounded-l-lg" />
                  )}
                  <div className="flex items-start justify-between mb-2">
                    <h3 className={`text-sm font-medium line-clamp-2 flex-1 mr-2 ${isSelected ? 'text-blue-900' : 'text-gray-900'
                      }`}>
                      {document.title}
                    </h3>
                    {document.permission === "owner" && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setDocumentToDelete(document._id);
                          setShowDeleteDialog(true);
                        }}
                        className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0 text-gray-400 hover:text-red-600"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <Badge
                      variant={getPermissionColor(document.permission) as any}
                      className="gap-1 text-xs"
                    >
                      {getPermissionIcon(document.permission)}
                      {document.permission}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {new Date(document._creationTime).toLocaleDateString()}
                    </span>
                  </div>

                  {document.owner && document.permission !== "owner" && (
                    <div className="text-xs text-muted-foreground mt-1">
                      Owned by {document.owner.name || document.owner.email}
                    </div>
                  )}
                </a>
              );
            })}
          </div>
        )}
      </div>

      {/* Create Document Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create New Document</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="document-title" className="text-sm font-medium">
                Document Name
              </label>
              <Input
                id="document-title"
                placeholder="Enter document name..."
                value={newDocumentTitle}
                onChange={(e) => {
                  setNewDocumentTitle(e.target.value);
                  if (titleError) setTitleError("");
                }}
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !titleError && newDocumentTitle.trim()) {
                    handleCreateDocument();
                  }
                }}
                className={titleError ? "border-red-500" : ""}
                autoFocus
              />
              {titleError && (
                <p className="text-sm text-red-600">{titleError}</p>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowCreateDialog(false);
                setNewDocumentTitle("");
                setTitleError("");
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateDocument}
              disabled={isCreating || !newDocumentTitle.trim()}
            >
              {isCreating ? "Creating..." : "Create Document"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Document Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Delete Document</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Are you sure you want to delete this document? This action cannot be undone.
            </p>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteDocument}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div >
  );
}

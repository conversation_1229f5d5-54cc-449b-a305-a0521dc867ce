import { useQuery } from 'convex/react';
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { CollaborativeEditor } from "./CollaborativeEditor";
import { ScreenplayEditor } from "./ScreenplayEditor";
import { CollectionEditor } from "./CollectionEditor";

interface DocumentEditorProps {
  documentId: Id<"documents">;
}

export function DocumentEditor({ documentId }: DocumentEditorProps) {
  const document = useQuery(api.documents.getDocument, { id: documentId });
  const permission = useQuery(api.sharing.getDocumentPermission, { documentId });

  if (!document || !permission) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Route to appropriate editor based on document type
  switch (document.documentType) {
    case "screenplay":
      return <ScreenplayEditor documentId={documentId} />;
    case "collection":
      return <CollectionEditor documentId={documentId} />;
    case "research":
    default:
      return <CollaborativeEditor documentId={documentId} />;
  }
}

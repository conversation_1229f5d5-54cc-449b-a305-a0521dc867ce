/**
 * Comment sidebar component showing all comments for a document
 */

import React, { useState, useMemo } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "./ui/card";
import { <PERSON><PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { Separator } from "./ui/separator";
import { CommentThread } from "./CommentThread";
import { CommentInput } from "./CommentInput";
import { 
  MessageSquare, 
  X, 
  Filter,
  CheckCircle,
  Clock,
  Plus
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { Id } from "../../convex/_generated/dataModel";

interface CommentUser {
  _id: string;
  name?: string;
  email?: string;
  image?: string;
}

interface CommentReply {
  _id: string;
  commentId: string;
  userId: string;
  content: string;
  timestamp: number;
  user?: CommentUser | null;
}

interface Comment {
  _id: string;
  documentId: string;
  userId: string;
  content: string;
  position: number;
  selection: {
    from: number;
    to: number;
  };
  selectedText: string;
  isResolved?: boolean;
  timestamp: number;
  user?: CommentUser | null;
  replies?: CommentReply[];
}

type FilterType = "all" | "active" | "resolved";

interface CommentSidebarProps {
  comments: Comment[];
  currentUserId?: string;
  selectedCommentId?: string;
  isVisible: boolean;
  canResolve?: boolean;
  onClose: () => void;
  onCreateComment: (content: string) => Promise<any>;
  onUpdateComment: (commentId: Id<"comments">, content: string) => Promise<boolean>;
  onDeleteComment: (commentId: Id<"comments">) => Promise<boolean>;
  onCreateReply: (commentId: Id<"comments">, content: string) => Promise<any>;
  onUpdateReply: (replyId: Id<"commentReplies">, content: string) => Promise<boolean>;
  onDeleteReply: (replyId: Id<"commentReplies">) => Promise<boolean>;
  onResolveComment: (commentId: Id<"comments">, isResolved: boolean) => Promise<boolean>;
  onSelectComment: (commentId: string) => void;
}

export function CommentSidebar({
  comments,
  currentUserId,
  selectedCommentId,
  isVisible,
  canResolve = false,
  onClose,
  onCreateComment,
  onUpdateComment,
  onDeleteComment,
  onCreateReply,
  onUpdateReply,
  onDeleteReply,
  onResolveComment,
  onSelectComment,
}: CommentSidebarProps) {
  const [filter, setFilter] = useState<FilterType>("all");
  const [showNewCommentInput, setShowNewCommentInput] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Filter comments based on current filter
  const filteredComments = useMemo(() => {
    switch (filter) {
      case "active":
        return comments.filter(comment => !comment.isResolved);
      case "resolved":
        return comments.filter(comment => comment.isResolved);
      default:
        return comments;
    }
  }, [comments, filter]);

  // Sort comments by timestamp (newest first)
  const sortedComments = useMemo(() => {
    return [...filteredComments].sort((a, b) => b.timestamp - a.timestamp);
  }, [filteredComments]);

  // Count comments by status
  const commentCounts = useMemo(() => {
    const active = comments.filter(c => !c.isResolved).length;
    const resolved = comments.filter(c => c.isResolved).length;
    return { active, resolved, total: comments.length };
  }, [comments]);

  const handleCreateComment = async (content: string) => {
    setIsSubmitting(true);
    try {
      const success = await onCreateComment(content);
      if (success) {
        setShowNewCommentInput(false);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isVisible) return null;

  return (
    <div
      data-testid="comment-sidebar"
      className="fixed right-0 top-0 h-full w-96 max-w-[90vw] sm:max-w-[400px] bg-background border-l border-border shadow-lg z-50 flex flex-col"
    >
      {/* Header */}
      <CardHeader className="flex-shrink-0 pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <MessageSquare className="h-5 w-5" />
            Comments
            <Badge variant="secondary" className="ml-2">
              {commentCounts.total}
            </Badge>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        {/* Filter and Stats */}
        <div className="flex items-center justify-between mt-3">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="gap-2">
                <Filter className="h-4 w-4" />
                {filter === "all" ? "All" : filter === "active" ? "Active" : "Resolved"}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <DropdownMenuItem onClick={() => setFilter("all")}>
                All ({commentCounts.total})
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilter("active")}>
                <Clock className="h-4 w-4 mr-2" />
                Active ({commentCounts.active})
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilter("resolved")}>
                <CheckCircle className="h-4 w-4 mr-2" />
                Resolved ({commentCounts.resolved})
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          <Button 
            size="sm" 
            onClick={() => setShowNewCommentInput(true)}
            disabled={showNewCommentInput}
          >
            <Plus className="h-4 w-4 mr-1" />
            New
          </Button>
        </div>
      </CardHeader>

      {/* Content */}
      <div className="flex-1 overflow-hidden flex flex-col">
        <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
          {/* New Comment Input */}
          {showNewCommentInput && (
            <div className="mb-4">
              <CommentInput
                placeholder="Select text in the editor and add your comment..."
                onSubmit={handleCreateComment}
                onCancel={() => setShowNewCommentInput(false)}
                isSubmitting={isSubmitting}
                autoFocus
                showCancel
                submitLabel="Add Comment"
              />
            </div>
          )}

          {/* Comments List */}
          {sortedComments.length === 0 ? (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="font-medium text-muted-foreground mb-2">
                {filter === "all" 
                  ? "No comments yet" 
                  : filter === "active" 
                    ? "No active comments" 
                    : "No resolved comments"
                }
              </h3>
              <p className="text-sm text-muted-foreground">
                {filter === "all" 
                  ? "Select text in the editor and right-click to add the first comment."
                  : filter === "active"
                    ? "All comments have been resolved."
                    : "No comments have been resolved yet."
                }
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {sortedComments.map((comment) => (
                <CommentThread
                  data-testid="comment-thread"
                  key={comment._id}
                  comment={comment}
                  currentUserId={currentUserId}
                  isSelected={selectedCommentId === comment._id}
                  canResolve={canResolve}
                  onUpdateComment={onUpdateComment}
                  onDeleteComment={onDeleteComment}
                  onCreateReply={onCreateReply}
                  onUpdateReply={onUpdateReply}
                  onDeleteReply={onDeleteReply}
                  onResolveComment={onResolveComment}
                  onSelectComment={onSelectComment}
                />
              ))}
            </div>
          )}
        </CardContent>
      </div>

      {/* Footer with quick stats */}
      {commentCounts.total > 0 && (
        <>
          <Separator />
          <div className="p-4 bg-muted/30">
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>{commentCounts.active} active</span>
              <span>{commentCounts.resolved} resolved</span>
              <span>{commentCounts.total} total</span>
            </div>
          </div>
        </>
      )}
    </div>
  );
}

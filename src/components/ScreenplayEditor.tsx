import { useQuery } from "convex/react";
import { useBlockNoteSync } from "@convex-dev/prosemirror-sync/blocknote";
import { BlockNoteView } from "@blocknote/mantine";
import "@blocknote/core/fonts/inter.css";
import "@blocknote/mantine/style.css";
import {
  SideMenuController,
  SideMenu,
  AddBlockButton,
  DragHandleButton,
} from "@blocknote/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useState, useEffect, useCallback } from "react";
import { useCollaborativeCursors } from "../hooks/useCollaborativeCursors";
import { useComments } from "../hooks/useComments";
import { CommentSidebar } from "./CommentSidebar";
import { CommentPopover } from "./CommentPopover";
import { DocumentHeader } from "./DocumentHeader";
import { ScreenplayToolbar } from "./ScreenplayToolbar";
import { PaginatedEditor } from "./PaginatedEditor";
import { useScreenplayFormatting } from "../hooks/useScreenplayFormatting";
import { generateScreenplayCSS } from "../lib/screenplaySchema";
import { AlertCircle } from "lucide-react";

interface ScreenplayEditorProps {
  documentId: Id<"documents">;
}

export function ScreenplayEditor({ documentId }: ScreenplayEditorProps) {
  const documentData = useQuery(api.documents.getDocument, { id: documentId });
  const permission = useQuery(api.sharing.getDocumentPermission, { documentId });
  const currentUser = useQuery(api.auth.loggedInUser);

  // Check if user has write permission for the editor
  const isReadOnly = permission?.permission === "read";

  // Use the sync hook to get the editor
  const sync = useBlockNoteSync(api.prosemirror, documentId);

  // Debug logging
  useEffect(() => {
    console.log('ScreenplayEditor Debug:', {
      documentId,
      documentData,
      permission,
      currentUser,
      syncEditor: !!sync.editor,
      syncLoading: sync.isLoading
    });
  }, [documentId, documentData, permission, currentUser, sync.editor, sync.isLoading]);

  // Comment system state
  const [showCommentSidebar, setShowCommentSidebar] = useState(false);
  const [commentPopover, setCommentPopover] = useState<{
    isVisible: boolean;
    position: { x: number; y: number };
    selectedText: string;
  }>({
    isVisible: false,
    position: { x: 0, y: 0 },
    selectedText: "",
  });

  // Page state
  const [, setCurrentPage] = useState(1);
  const [, setTotalPages] = useState(1);

  // Collaborative cursors
  const { cursors } = useCollaborativeCursors({
    documentId,
    editor: sync.editor,
    isReadOnly,
  });

  // Comments
  const {
    comments,
    createComment,
    updateComment,
    deleteComment,
    createReply,
    updateReply,
    deleteReply,
    resolveComment,
  } = useComments({
    documentId,
    editor: sync.editor,
    isReadOnly,
  });

  // Screenplay formatting - only initialize when editor is ready
  const {
    currentElementType,
    formatAsElement,
  } = useScreenplayFormatting({
    editor: sync.editor,
    isReadOnly,
  });



  // Handle page changes
  const handlePageChange = useCallback((page: number, total: number) => {
    setCurrentPage(page);
    setTotalPages(total);
  }, []);

  // Handle text selection for comments
  const handleTextSelection = useCallback((selectedText: string, position: { x: number; y: number }) => {
    if (selectedText.trim() && !isReadOnly) {
      setCommentPopover({
        isVisible: true,
        position,
        selectedText: selectedText.trim(),
      });
    } else {
      setCommentPopover(prev => ({ ...prev, isVisible: false }));
    }
  }, [isReadOnly]);

  // Handle comment creation
  const handleCreateComment = useCallback(async (content: string) => {
    if (!sync.editor || !currentUser) return;

    try {
      await createComment(content);

      setCommentPopover(prev => ({ ...prev, isVisible: false }));
    } catch (error) {
      console.error("Failed to create comment:", error);
    }
  }, [sync.editor, currentUser, createComment, commentPopover.selectedText]);

  // Setup collaborative cursors plugin
  useEffect(() => {
    if (sync.editor && cursors.length > 0) {
      // Note: Plugin integration would need to be added to BlockNote configuration
      // For now, cursors are handled by the useCollaborativeCursors hook
    }
  }, [sync.editor, cursors, currentUser]);

  // Setup comment plugin
  useEffect(() => {
    if (sync.editor && comments.length > 0) {
      // Note: Plugin integration would need to be added to BlockNote configuration
      // For now, comments are handled by the useComments hook
    }
  }, [sync.editor, comments, currentUser, handleTextSelection]);

  // Inject screenplay CSS
  useEffect(() => {
    const styleId = 'screenplay-styles';
    let styleElement = document.getElementById(styleId);

    if (!styleElement) {
      styleElement = document.createElement('style');
      styleElement.id = styleId;
      document.head.appendChild(styleElement);
    }

    styleElement.textContent = generateScreenplayCSS();

    return () => {
      const element = document.getElementById(styleId);
      if (element) {
        element.remove();
      }
    };
  }, []);

  // Loading state
  if (!documentData || !permission || sync.isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Error state - document not found
  if (!documentData) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Document Not Found</h3>
          <p className="text-gray-600">The screenplay document could not be loaded.</p>
        </div>
      </div>
    );
  }

  // Error state - wrong document type
  if (documentData.documentType !== "screenplay") {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Wrong Document Type</h3>
          <p className="text-gray-600">
            This document is not a screenplay. Document type: {documentData.documentType}
          </p>
        </div>
      </div>
    );
  }

  // Error state - editor failed to initialize
  if (!sync.editor) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Editor Failed to Load</h3>
          <p className="text-gray-600">The screenplay editor could not be initialized.</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Reload Page
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col h-full relative" data-testid="screenplay-editor">
      <DocumentHeader
        documentId={documentId}
      />

      <div className="flex-1 flex relative">
        {/* Main Editor Area */}
        <div className="flex-1 flex flex-col">
          {/* Screenplay Toolbar */}
          <ScreenplayToolbar
            onFormatElement={formatAsElement}
            onToggleComments={() => setShowCommentSidebar(!showCommentSidebar)}
            commentsCount={comments.length}
            showComments={showCommentSidebar}
            currentElementType={currentElementType}
            disabled={isReadOnly}
          />

          {/* Paginated Editor */}
          <PaginatedEditor
            onPageChange={handlePageChange}
            className="screenplay-editor"
          >
            <BlockNoteView
              editor={sync.editor}
              theme="light"
              className="screenplay-content"
              editable={!isReadOnly}
              formattingToolbar={false}
              sideMenu={false}
            >
              {!isReadOnly && sync.editor && (
                <SideMenuController
                  key="screenplay-side-menu"
                  sideMenu={(props) => (
                    <SideMenu {...props}>
                      <AddBlockButton {...props} />
                      <DragHandleButton {...props} />
                    </SideMenu>
                  )}
                />
              )}
            </BlockNoteView>
          </PaginatedEditor>
        </div>

        {/* Comment Sidebar */}
        {showCommentSidebar && (
          <CommentSidebar
            comments={comments}
            currentUserId={currentUser?._id}
            selectedCommentId={undefined}
            isVisible={showCommentSidebar}
            canResolve={!isReadOnly}
            onClose={() => setShowCommentSidebar(false)}
            onCreateComment={createComment}
            onUpdateComment={updateComment}
            onDeleteComment={deleteComment}
            onCreateReply={createReply}
            onUpdateReply={updateReply}
            onDeleteReply={deleteReply}
            onResolveComment={resolveComment}
            onSelectComment={() => {}}
          />
        )}

        {/* Comment Popover */}
        {commentPopover.isVisible && (
          <CommentPopover
            isVisible={commentPopover.isVisible}
            position={commentPopover.position}
            selectedText={commentPopover.selectedText}
            onCreateComment={handleCreateComment}
            onClose={() => setCommentPopover(prev => ({ ...prev, isVisible: false }))}
          />
        )}
      </div>
    </div>
  );
}

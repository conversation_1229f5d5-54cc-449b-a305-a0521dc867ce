/**
 * Unit tests for ScrollArea component
 */

import { render, screen } from '@testing-library/react';
import { ScrollArea } from '../ui/scroll-area';

describe('ScrollArea', () => {
  it('should render with children', () => {
    render(
      <ScrollArea>
        <div data-testid="scroll-content">Test content</div>
      </ScrollArea>
    );
    
    expect(screen.getByTestId('scroll-content')).toBeInTheDocument();
  });

  it('should apply custom className', () => {
    render(
      <ScrollArea className="custom-class">
        <div>Content</div>
      </ScrollArea>
    );
    
    const scrollArea = document.querySelector('[data-radix-scroll-area-root]');
    expect(scrollArea).toHaveClass('custom-class');
  });

  it('should render scroll viewport', () => {
    render(
      <ScrollArea>
        <div>Content</div>
      </ScrollArea>
    );
    
    const viewport = document.querySelector('[data-radix-scroll-area-viewport]');
    expect(viewport).toBeInTheDocument();
    expect(viewport).toHaveClass('h-full', 'w-full', 'rounded-[inherit]');
  });

  it('should render scrollbar', () => {
    render(
      <ScrollArea>
        <div>Content</div>
      </ScrollArea>
    );
    
    const scrollbar = document.querySelector('[data-radix-scroll-area-scrollbar]');
    expect(scrollbar).toBeInTheDocument();
  });

  it('should render with proper structure for editor scrolling', () => {
    render(
      <ScrollArea className="h-[calc(100vh-300px)] min-h-[500px] editor-scroll-area">
        <div className="p-6">
          <div data-testid="editor-content">Editor content</div>
        </div>
      </ScrollArea>
    );
    
    // Check ScrollArea has proper classes
    const scrollArea = document.querySelector('[data-radix-scroll-area-root]');
    expect(scrollArea).toHaveClass('h-[calc(100vh-300px)]');
    expect(scrollArea).toHaveClass('min-h-[500px]');
    expect(scrollArea).toHaveClass('editor-scroll-area');
    
    // Check content structure
    const paddedContent = document.querySelector('.p-6');
    expect(paddedContent).toBeInTheDocument();
    expect(screen.getByTestId('editor-content')).toBeInTheDocument();
  });
});

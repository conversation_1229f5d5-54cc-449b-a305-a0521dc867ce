/**
 * Tests for CommentInput component
 */

import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CommentInput } from '../CommentInput';

describe('CommentInput', () => {
  const mockOnSubmit = vi.fn();
  const mockOnCancel = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render with placeholder', () => {
    render(
      <CommentInput
        placeholder="Add a comment..."
        onSubmit={mockOnSubmit}
      />
    );

    expect(screen.getByPlaceholderText('Add a comment...')).toBeInTheDocument();
  });

  it('should render with initial value', () => {
    render(
      <CommentInput
        initialValue="Initial comment"
        onSubmit={mockOnSubmit}
      />
    );

    expect(screen.getByDisplayValue('Initial comment')).toBeInTheDocument();
  });

  it('should expand on focus', async () => {
    const user = userEvent.setup();
    
    render(
      <CommentInput
        placeholder="Add a comment..."
        onSubmit={mockOnSubmit}
      />
    );

    const textarea = screen.getByPlaceholderText('Add a comment...');
    await user.click(textarea);

    // Check that submit button appears when expanded
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /comment/i })).toBeInTheDocument();
    });
  });

  it('should submit comment on form submission', async () => {
    const user = userEvent.setup();
    mockOnSubmit.mockResolvedValue(true);

    render(
      <CommentInput
        placeholder="Add a comment..."
        onSubmit={mockOnSubmit}
      />
    );

    const textarea = screen.getByPlaceholderText('Add a comment...');
    await user.type(textarea, 'Test comment');
    
    const submitButton = screen.getByRole('button', { name: /comment/i });
    await user.click(submitButton);

    expect(mockOnSubmit).toHaveBeenCalledWith('Test comment');
  });

  it('should submit comment on Cmd+Enter', async () => {
    const user = userEvent.setup();
    mockOnSubmit.mockResolvedValue(true);

    render(
      <CommentInput
        placeholder="Add a comment..."
        onSubmit={mockOnSubmit}
      />
    );

    const textarea = screen.getByPlaceholderText('Add a comment...');
    await user.type(textarea, 'Test comment');
    await user.keyboard('{Meta>}{Enter}{/Meta}');

    expect(mockOnSubmit).toHaveBeenCalledWith('Test comment');
  });

  it('should cancel on Escape key', async () => {
    const user = userEvent.setup();

    render(
      <CommentInput
        placeholder="Add a comment..."
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
        showCancel
      />
    );

    const textarea = screen.getByPlaceholderText('Add a comment...');
    await user.type(textarea, 'Test comment');
    await user.keyboard('{Escape}');

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('should show cancel button when showCancel is true', () => {
    render(
      <CommentInput
        placeholder="Add a comment..."
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
        showCancel
        autoFocus
      />
    );

    expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
  });

  it('should disable submit button when content is empty', async () => {
    const user = userEvent.setup();

    render(
      <CommentInput
        placeholder="Add a comment..."
        onSubmit={mockOnSubmit}
        autoFocus
      />
    );

    const submitButton = screen.getByRole('button', { name: /comment/i });
    expect(submitButton).toBeDisabled();

    const textarea = screen.getByPlaceholderText('Add a comment...');
    await user.type(textarea, 'Test');
    expect(submitButton).not.toBeDisabled();

    await user.clear(textarea);
    expect(submitButton).toBeDisabled();
  });

  it('should disable form when submitting', async () => {
    const user = userEvent.setup();
    let resolveSubmit: (value: any) => void;
    const submitPromise = new Promise(resolve => {
      resolveSubmit = resolve;
    });
    mockOnSubmit.mockReturnValue(submitPromise);

    render(
      <CommentInput
        placeholder="Add a comment..."
        onSubmit={mockOnSubmit}
        autoFocus
      />
    );

    const textarea = screen.getByPlaceholderText('Add a comment...');
    await user.type(textarea, 'Test comment');
    
    const submitButton = screen.getByRole('button', { name: /comment/i });
    await user.click(submitButton);

    // Should show submitting state
    expect(screen.getByRole('button', { name: /submitting/i })).toBeInTheDocument();
    expect(textarea).toBeDisabled();

    // Resolve the promise
    resolveSubmit!(true);
    
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /comment/i })).toBeInTheDocument();
    });
  });

  it('should clear content after successful submission', async () => {
    const user = userEvent.setup();
    mockOnSubmit.mockResolvedValue(true);

    render(
      <CommentInput
        placeholder="Add a comment..."
        onSubmit={mockOnSubmit}
        autoFocus
      />
    );

    const textarea = screen.getByPlaceholderText('Add a comment...');
    await user.type(textarea, 'Test comment');
    
    const submitButton = screen.getByRole('button', { name: /comment/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(textarea).toHaveValue('');
    });
  });

  it('should use custom submit label', () => {
    render(
      <CommentInput
        placeholder="Add a comment..."
        onSubmit={mockOnSubmit}
        submitLabel="Update"
        autoFocus
      />
    );

    expect(screen.getByRole('button', { name: /update/i })).toBeInTheDocument();
  });

  it('should handle submission errors gracefully', async () => {
    const user = userEvent.setup();
    const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {});
    mockOnSubmit.mockRejectedValue(new Error('Submission failed'));

    render(
      <CommentInput
        placeholder="Add a comment..."
        onSubmit={mockOnSubmit}
        autoFocus
      />
    );

    const textarea = screen.getByPlaceholderText('Add a comment...');
    await user.type(textarea, 'Test comment');
    
    const submitButton = screen.getByRole('button', { name: /comment/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(consoleError).toHaveBeenCalledWith('Error submitting comment:', expect.any(Error));
    });

    // Content should remain in textarea after error
    expect(textarea).toHaveValue('Test comment');

    consoleError.mockRestore();
  });
});

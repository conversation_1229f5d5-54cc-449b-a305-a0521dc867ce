/**
 * Test file for ScreenplayEditor component
 * Tests basic rendering and error handling
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { ScreenplayEditor } from '../ScreenplayEditor';

// Mock the Convex hooks
vi.mock('convex/react', () => ({
  useQuery: vi.fn(),
  useMutation: vi.fn(),
}));

// Mock the BlockNote sync hook
vi.mock('@convex-dev/prosemirror-sync/blocknote', () => ({
  useBlockNoteSync: vi.fn(),
}));

// Mock the custom hooks
vi.mock('../hooks/useCollaborativeCursors', () => ({
  useCollaborativeCursors: vi.fn(() => ({ cursors: [] })),
}));

vi.mock('../hooks/useComments', () => ({
  useComments: vi.fn(() => ({
    comments: [],
    createComment: vi.fn(),
    updateComment: vi.fn(),
    deleteComment: vi.fn(),
    createReply: vi.fn(),
    updateReply: vi.fn(),
    deleteReply: vi.fn(),
    resolveComment: vi.fn(),
  })),
}));

vi.mock('../hooks/useScreenplayFormatting', () => ({
  useScreenplayFormatting: vi.fn(() => ({
    currentElementType: 'action',
    formatAsElement: vi.fn(),
  })),
}));

// Mock the components
vi.mock('../DocumentHeader', () => ({
  DocumentHeader: () => <div data-testid="document-header">Document Header</div>,
}));

vi.mock('../ScreenplayToolbar', () => ({
  ScreenplayToolbar: () => <div data-testid="screenplay-toolbar">Screenplay Toolbar</div>,
}));

vi.mock('../PaginatedEditor', () => ({
  PaginatedEditor: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="paginated-editor">{children}</div>
  ),
}));

vi.mock('../CommentSidebar', () => ({
  CommentSidebar: () => <div data-testid="comment-sidebar">Comment Sidebar</div>,
}));

vi.mock('../CommentPopover', () => ({
  CommentPopover: () => <div data-testid="comment-popover">Comment Popover</div>,
}));

// Mock BlockNote components
vi.mock('@blocknote/mantine', () => ({
  BlockNoteView: ({ children }: { children?: React.ReactNode }) => (
    <div data-testid="blocknote-view">{children}</div>
  ),
}));

vi.mock('@blocknote/react', () => ({
  SideMenuController: ({ children }: { children?: React.ReactNode }) => (
    <div data-testid="side-menu-controller">{children}</div>
  ),
  SideMenu: ({ children }: { children?: React.ReactNode }) => (
    <div data-testid="side-menu">{children}</div>
  ),
  AddBlockButton: () => <button data-testid="add-block-button">+</button>,
  DragHandleButton: () => <button data-testid="drag-handle-button">⠿</button>,
}));

import { useQuery, useMutation } from 'convex/react';
import { useBlockNoteSync } from '@convex-dev/prosemirror-sync/blocknote';

const mockUseQuery = useQuery as any;
const mockUseMutation = useMutation as any;
const mockUseBlockNoteSync = useBlockNoteSync as any;

describe('ScreenplayEditor', () => {
  const mockDocumentId = 'test-document-id' as any;

  beforeEach(() => {
    vi.clearAllMocks();
    // Set up default mocks
    mockUseMutation.mockReturnValue(vi.fn());
  });

  it('should show loading state when data is loading', () => {
    mockUseQuery.mockReturnValue(null);
    mockUseBlockNoteSync.mockReturnValue({
      editor: null,
      isLoading: true,
    });

    render(<ScreenplayEditor documentId={mockDocumentId} />);
    
    expect(screen.getByRole('status', { hidden: true })).toBeInTheDocument();
  });

  it('should show error when document is not found', () => {
    mockUseQuery
      .mockReturnValueOnce(null) // documentData
      .mockReturnValueOnce({ permission: 'write' }) // permission
      .mockReturnValueOnce({ _id: 'user1' }); // currentUser
    
    mockUseBlockNoteSync.mockReturnValue({
      editor: {},
      isLoading: false,
    });

    render(<ScreenplayEditor documentId={mockDocumentId} />);
    
    expect(screen.getByText('Document Not Found')).toBeInTheDocument();
  });

  it('should show error when document is not a screenplay', () => {
    mockUseQuery
      .mockReturnValueOnce({ documentType: 'research', title: 'Test Doc' }) // documentData
      .mockReturnValueOnce({ permission: 'write' }) // permission
      .mockReturnValueOnce({ _id: 'user1' }); // currentUser
    
    mockUseBlockNoteSync.mockReturnValue({
      editor: {},
      isLoading: false,
    });

    render(<ScreenplayEditor documentId={mockDocumentId} />);
    
    expect(screen.getByText('Wrong Document Type')).toBeInTheDocument();
    expect(screen.getByText(/Document type: research/)).toBeInTheDocument();
  });

  it('should show error when editor fails to initialize', () => {
    mockUseQuery
      .mockReturnValueOnce({ documentType: 'screenplay', title: 'Test Screenplay' }) // documentData
      .mockReturnValueOnce({ permission: 'write' }) // permission
      .mockReturnValueOnce({ _id: 'user1' }); // currentUser
    
    mockUseBlockNoteSync.mockReturnValue({
      editor: null,
      isLoading: false,
    });

    render(<ScreenplayEditor documentId={mockDocumentId} />);
    
    expect(screen.getByText('Editor Failed to Load')).toBeInTheDocument();
    expect(screen.getByText('Reload Page')).toBeInTheDocument();
  });

  it('should render successfully with valid screenplay document', () => {
    mockUseQuery
      .mockReturnValueOnce({ documentType: 'screenplay', title: 'Test Screenplay' }) // documentData
      .mockReturnValueOnce({ permission: 'write' }) // permission
      .mockReturnValueOnce({ _id: 'user1' }); // currentUser
    
    mockUseBlockNoteSync.mockReturnValue({
      editor: { prosemirrorView: {} },
      isLoading: false,
    });

    render(<ScreenplayEditor documentId={mockDocumentId} />);
    
    expect(screen.getByTestId('document-header')).toBeInTheDocument();
    expect(screen.getByTestId('screenplay-toolbar')).toBeInTheDocument();
    expect(screen.getByTestId('paginated-editor')).toBeInTheDocument();
    expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
  });
});

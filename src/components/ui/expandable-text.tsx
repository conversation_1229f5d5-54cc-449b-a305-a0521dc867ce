/**
 * Expandable text component with truncation and overflow handling
 */

import React, { useState, useRef, useEffect } from "react";
import { Button } from "./button";
import { ChevronDown, ChevronUp } from "lucide-react";
import { cn } from "../../lib/utils";

interface ExpandableTextProps {
  children: string | string[];
  maxLines?: number;
  maxHeight?: number;
  className?: string;
  showMoreText?: string;
  showLessText?: string;
  enableScrolling?: boolean;
  maxScrollHeight?: number;
}

export function ExpandableText({
  children,
  maxLines = 3,
  maxHeight,
  className = "",
  showMoreText = "Show more",
  showLessText = "Show less",
  enableScrolling = false,
  maxScrollHeight = 200,
}: ExpandableTextProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [shouldTruncate, setShouldTruncate] = useState(false);
  const [isOverflowing, setIsOverflowing] = useState(false);
  const textRef = useRef<HTMLDivElement>(null);
  const measureRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const checkOverflow = () => {
      if (!textRef.current || !measureRef.current) return;

      const textElement = textRef.current;
      const measureElement = measureRef.current;

      // Check if content overflows based on line height
      const lineHeight = parseInt(getComputedStyle(textElement).lineHeight);
      const maxHeightPx = maxHeight || (lineHeight * maxLines);
      
      const contentHeight = measureElement.scrollHeight;
      const shouldTruncateContent = contentHeight > maxHeightPx;
      
      setShouldTruncate(shouldTruncateContent);
      
      // Check if expanded content would need scrolling
      if (enableScrolling && contentHeight > maxScrollHeight) {
        setIsOverflowing(true);
      }
    };

    checkOverflow();
    
    // Recheck on window resize
    window.addEventListener('resize', checkOverflow);
    return () => window.removeEventListener('resize', checkOverflow);
  }, [children, maxLines, maxHeight, enableScrolling, maxScrollHeight]);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const getDisplayStyle = () => {
    if (!shouldTruncate || isExpanded) {
      if (enableScrolling && isOverflowing && isExpanded) {
        return {
          maxHeight: `${maxScrollHeight}px`,
          overflowY: 'auto' as const,
        };
      }
      return {};
    }

    if (maxHeight) {
      return {
        maxHeight: `${maxHeight}px`,
        overflow: 'hidden',
      };
    }

    return {
      display: '-webkit-box',
      WebkitLineClamp: maxLines,
      WebkitBoxOrient: 'vertical' as const,
      overflow: 'hidden',
    };
  };

  return (
    <div className={cn("text-expandable", className)}>
      {/* Hidden element for measuring content height */}
      <div
        ref={measureRef}
        className="absolute opacity-0 pointer-events-none whitespace-pre-wrap text-sm"
        style={{ width: textRef.current?.offsetWidth || 'auto' }}
        aria-hidden="true"
      >
        {children}
      </div>

      {/* Visible content */}
      <div
        ref={textRef}
        className={cn(
          "whitespace-pre-wrap text-sm transition-all duration-200",
          enableScrolling && isOverflowing && isExpanded && "text-scrollable"
        )}
        style={getDisplayStyle()}
      >
        {children}
      </div>

      {/* Show more/less button */}
      {shouldTruncate && (
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleExpanded}
          className="h-auto p-0 mt-1 text-xs text-muted-foreground hover:text-foreground"
        >
          {isExpanded ? (
            <>
              <ChevronUp className="h-3 w-3 mr-1" />
              {showLessText}
            </>
          ) : (
            <>
              <ChevronDown className="h-3 w-3 mr-1" />
              {showMoreText}
            </>
          )}
        </Button>
      )}
    </div>
  );
}

/**
 * Specialized component for displaying quoted/selected text with proper overflow handling
 */
interface QuotedTextProps {
  children: string;
  maxLines?: number;
  className?: string;
}

export function QuotedText({ 
  children, 
  maxLines = 2, 
  className = "" 
}: QuotedTextProps) {
  return (
    <ExpandableText
      maxLines={maxLines}
      className={cn("text-sm italic text-muted-foreground", className)}
      showMoreText="Show full text"
      showLessText="Show less"
    >
      "{children}"
    </ExpandableText>
  );
}

/**
 * Specialized component for comment content with scrolling support
 */
interface CommentContentProps {
  children: string;
  maxLines?: number;
  enableScrolling?: boolean;
  className?: string;
}

export function CommentContent({ 
  children, 
  maxLines = 4, 
  enableScrolling = true,
  className = "" 
}: CommentContentProps) {
  return (
    <ExpandableText
      maxLines={maxLines}
      enableScrolling={enableScrolling}
      maxScrollHeight={150}
      className={cn("text-sm", className)}
      showMoreText="Read more"
      showLessText="Show less"
    >
      {children}
    </ExpandableText>
  );
}

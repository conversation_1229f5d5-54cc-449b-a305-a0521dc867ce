import { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { X, Share2, <PERSON>r<PERSON><PERSON>, Trash2, Crown, Eye, Edit } from "lucide-react";
import { toast } from "sonner";

interface DocumentSharingModalProps {
  documentId: Id<"documents">;
  isOpen: boolean;
  onClose: () => void;
}

export function DocumentSharingModal({ documentId, isOpen, onClose }: DocumentSharingModalProps) {
  const [email, setEmail] = useState("");
  const [permission, setPermission] = useState<"read" | "write">("read");
  const [isSharing, setIsSharing] = useState(false);

  const shares = useQuery(api.sharing.getDocumentShares, { documentId });
  const document = useQuery(api.documents.getDocument, { id: documentId });
  const loggedInUser = useQuery(api.auth.loggedInUser);
  const shareDocument = useMutation(api.sharing.shareDocument);
  const removeShare = useMutation(api.sharing.removeShare);

  const handleShare = async () => {
    if (!email.trim()) return;

    setIsSharing(true);
    try {
      await shareDocument({
        documentId,
        userEmail: email.trim(),
        permission,
      });
      setEmail("");
      toast.success("Document shared successfully!");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to share document");
    } finally {
      setIsSharing(false);
    }
  };

  const handleRemoveShare = async (userId: Id<"users">) => {
    try {
      await removeShare({ documentId, userId });
      toast.success("Access removed successfully!");
    } catch (error) {
      toast.error("Failed to remove access");
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <Share2 size={20} className="text-blue-600" />
            <h2 className="text-lg font-semibold">Share Document</h2>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-4 space-y-4">
          {/* Document Owner */}
          {loggedInUser && document && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                  <Crown size={16} className="text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-yellow-800">Document Owner</p>
                  <p className="text-sm text-yellow-700">
                    {loggedInUser.name || loggedInUser.email}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Share with new user */}
          <div className="space-y-3">
            <h3 className="font-medium text-gray-900">Add people</h3>
            <div className="flex gap-2">
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter email address..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                onKeyDown={(e) => {
                  if (e.key === "Enter") handleShare();
                }}
              />
              <select
                value={permission}
                onChange={(e) => setPermission(e.target.value as "read" | "write")}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              >
                <option value="read">Can view</option>
                <option value="write">Can edit</option>
              </select>
            </div>
            <button
              onClick={handleShare}
              disabled={!email.trim() || isSharing}
              className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              <UserPlus size={16} />
              {isSharing ? "Sharing..." : "Share"}
            </button>
          </div>

          {/* Current shares */}
          {shares && shares.length > 0 && (
            <div className="space-y-3">
              <h3 className="font-medium text-gray-900">People with access</h3>
              <div className="space-y-2">
                {shares.map((share) => (
                  <div
                    key={share._id}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-blue-600">
                          {share.user?.name?.charAt(0) || share.user?.email?.charAt(0) || "?"}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {share.user?.name || "Unknown User"}
                        </p>
                        <p className="text-xs text-gray-500">{share.user?.email}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1 text-xs text-gray-600">
                        {share.permission === "read" ? (
                          <>
                            <Eye size={12} />
                            <span>Can view</span>
                          </>
                        ) : (
                          <>
                            <Edit size={12} />
                            <span>Can edit</span>
                          </>
                        )}
                      </div>
                      <button
                        onClick={() => handleRemoveShare(share.sharedWithUserId)}
                        className="p-1 hover:bg-red-100 rounded text-red-600"
                        title="Remove access"
                      >
                        <Trash2 size={14} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { X, CheckCircle, XCircle, Clock, User } from "lucide-react";
import { toast } from "sonner";

interface PendingRequestsModalProps {
  documentId: Id<"documents">;
  isOpen: boolean;
  onClose: () => void;
}

export function PendingRequestsModal({ documentId, isOpen, onClose }: PendingRequestsModalProps) {
  const pendingRequests = useQuery(api.sharing.getPendingPermissionRequests, { documentId });
  const reviewRequest = useMutation(api.sharing.reviewPermissionRequest);

  const handleReview = async (requestId: Id<"permissionRequests">, action: "approve" | "deny") => {
    try {
      await reviewRequest({ requestId, action });
      toast.success(
        action === "approve" 
          ? "Permission request approved! User now has edit access." 
          : "Permission request denied."
      );
    } catch (error) {
      toast.error("Failed to process request");
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-lg mx-4">
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <Clock size={20} className="text-orange-600" />
            <h2 className="text-lg font-semibold">Permission Requests</h2>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-4">
          {!pendingRequests || pendingRequests.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Clock size={48} className="mx-auto mb-2 opacity-50" />
              <p>No pending permission requests</p>
            </div>
          ) : (
            <div className="space-y-4">
              {pendingRequests.map((request) => (
                <div
                  key={request._id}
                  className="border border-gray-200 rounded-lg p-4 space-y-3"
                >
                  <div className="flex items-start gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      {request.user?.name ? (
                        <span className="text-sm font-medium text-blue-600">
                          {request.user.name.charAt(0).toUpperCase()}
                        </span>
                      ) : (
                        <User size={16} className="text-blue-600" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium text-gray-900">
                          {request.user?.name || "Unknown User"}
                        </h3>
                        <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full">
                          Requesting Edit Access
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">{request.user?.email}</p>
                      <p className="text-xs text-gray-500 mt-1">
                        Requested {new Date(request._creationTime).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  {request.message && (
                    <div className="bg-gray-50 rounded-md p-3">
                      <p className="text-sm text-gray-700 font-medium mb-1">Message:</p>
                      <p className="text-sm text-gray-600">{request.message}</p>
                    </div>
                  )}

                  <div className="flex gap-2">
                    <button
                      onClick={() => handleReview(request._id, "approve")}
                      className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm"
                    >
                      <CheckCircle size={16} />
                      Approve
                    </button>
                    <button
                      onClick={() => handleReview(request._id, "deny")}
                      className="flex items-center gap-2 px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm"
                    >
                      <XCircle size={16} />
                      Deny
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

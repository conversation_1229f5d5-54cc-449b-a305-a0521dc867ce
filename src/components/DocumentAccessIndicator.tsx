import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { Crown, Eye, Edit, Users } from "lucide-react";

interface DocumentAccessIndicatorProps {
  documentId: Id<"documents">;
}

export function DocumentAccessIndicator({ documentId }: DocumentAccessIndicatorProps) {
  const permission = useQuery(api.sharing.getDocumentPermission, { documentId });

  if (!permission) return null;

  const getIcon = () => {
    switch (permission.permission) {
      case "owner":
        return <Crown size={14} className="text-yellow-600" />;
      case "write":
        return <Edit size={14} className="text-green-600" />;
      case "read":
        return <Eye size={14} className="text-blue-600" />;
      default:
        return <Users size={14} className="text-gray-600" />;
    }
  };

  const getLabel = () => {
    switch (permission.permission) {
      case "owner":
        return "Owner";
      case "write":
        return "Can edit";
      case "read":
        return "Can view";
      default:
        return "No access";
    }
  };

  const getColor = () => {
    switch (permission.permission) {
      case "owner":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "write":
        return "bg-green-100 text-green-800 border-green-200";
      case "read":
        return "bg-blue-100 text-blue-800 border-blue-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getColor()}`}>
      {getIcon()}
      <span>{getLabel()}</span>
    </div>
  );
}

/**
 * Test component for collaborative cursor functionality
 * This component helps verify that selection highlighting works correctly
 */

import React, { useEffect, useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';

interface CollaborativeCursorTestProps {
  documentId: Id<"documents">;
}

export function CollaborativeCursorTest({ documentId }: CollaborativeCursorTestProps) {
  const cursors = useQuery(api.presence.getCursors, { documentId });
  const [lastUpdate, setLastUpdate] = useState<number>(Date.now());

  useEffect(() => {
    const interval = setInterval(() => {
      setLastUpdate(Date.now());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  if (!cursors) {
    return (
      <div className="p-4 bg-gray-100 rounded-lg">
        <h3 className="font-semibold text-gray-700">Collaborative Cursors Debug</h3>
        <p className="text-gray-500">Loading cursor data...</p>
      </div>
    );
  }

  return (
    <div className="p-4 bg-gray-100 rounded-lg space-y-2">
      <h3 className="font-semibold text-gray-700">Collaborative Cursors Debug</h3>
      <p className="text-sm text-gray-600">
        Active collaborators: {cursors.length} | Last update: {new Date(lastUpdate).toLocaleTimeString()}
      </p>
      
      {cursors.length === 0 ? (
        <p className="text-gray-500 text-sm">No other users currently editing</p>
      ) : (
        <div className="space-y-2">
          {cursors.map((cursor) => (
            <div key={cursor.userId} className="bg-white p-2 rounded border text-sm">
              <div className="font-medium">{cursor.name}</div>
              <div className="text-gray-600">
                Position: {cursor.cursor.position}
                {cursor.cursor.selection && (
                  <span className="ml-2">
                    Selection: {cursor.cursor.selection.from} → {cursor.cursor.selection.to}
                    ({cursor.cursor.selection.to - cursor.cursor.selection.from} chars)
                  </span>
                )}
              </div>
              <div className="text-xs text-gray-400">
                Last updated: {new Date(cursor.cursor.timestamp).toLocaleTimeString()}
              </div>
            </div>
          ))}
        </div>
      )}
      
      <div className="mt-4 p-2 bg-blue-50 rounded text-xs">
        <strong>Testing Instructions:</strong>
        <ol className="list-decimal list-inside mt-1 space-y-1">
          <li>Open this document in another browser tab/window</li>
          <li>Make text selections in both windows simultaneously</li>
          <li>Verify that selections don't flicker or disappear</li>
          <li>Check that multiple selections are visible at the same time</li>
          <li>Observe the debug info above for real-time updates</li>
        </ol>
      </div>
    </div>
  );
}

/**
 * Comment popover component for quick comment creation
 */

import React, { useState, useEffect, useRef } from "react";
import { Card, CardContent } from "./ui/card";
import { CommentInput } from "./CommentInput";
import { Button } from "./ui/button";
import { MessageSquare, X } from "lucide-react";
import { QuotedText } from "./ui/expandable-text";

interface CommentPopoverProps {
  isVisible: boolean;
  position: { x: number; y: number };
  selectedText: string;
  onCreateComment: (content: string) => Promise<any>;
  onClose: () => void;
}

export function CommentPopover({
  isVisible,
  position,
  selectedText,
  onCreateComment,
  onClose,
}: CommentPopoverProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const popoverRef = useRef<HTMLDivElement>(null);

  // <PERSON>le click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isVisible) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isVisible, onClose]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    if (isVisible) {
      document.addEventListener("keydown", handleEscape);
      return () => document.removeEventListener("keydown", handleEscape);
    }
  }, [isVisible, onClose]);

  const handleSubmit = async (content: string) => {
    setIsSubmitting(true);
    try {
      const success = await onCreateComment(content);
      if (success) {
        onClose();
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isVisible) return null;

  // Calculate position to keep popover in viewport with responsive sizing
  const popoverWidth = Math.min(400, window.innerWidth * 0.9); // Max 400px or 90% of viewport
  const popoverHeight = 350; // Approximate popover height

  const adjustedPosition = {
    x: Math.max(10, Math.min(position.x, window.innerWidth - popoverWidth - 10)),
    y: Math.max(10, Math.min(position.y, window.innerHeight - popoverHeight - 10)),
  };

  return (
    <div
      ref={popoverRef}
      data-testid="comment-popover"
      className="fixed z-50 w-96 max-w-[90vw] max-h-[80vh]"
      style={{
        left: adjustedPosition.x,
        top: adjustedPosition.y,
      }}
    >
      <Card className="shadow-lg border-2 flex flex-col max-h-full">
        <CardContent className="p-4 overflow-y-auto text-scrollable">
          {/* Header */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-sm">Add Comment</span>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose} className="h-6 w-6 p-0">
              <X className="h-3 w-3" />
            </Button>
          </div>

          {/* Selected Text Preview */}
          {selectedText && (
            <div className="mb-3 p-2 bg-muted rounded-md border-l-4 border-blue-500">
              <div className="text-xs text-muted-foreground mb-1">Selected text:</div>
              <QuotedText maxLines={3}>
                {selectedText}
              </QuotedText>
            </div>
          )}

          {/* Comment Input */}
          <CommentInput
            placeholder="What do you think about this?"
            onSubmit={handleSubmit}
            onCancel={onClose}
            isSubmitting={isSubmitting}
            autoFocus
            showCancel
            submitLabel="Add Comment"
          />
        </CardContent>
      </Card>
    </div>
  );
}

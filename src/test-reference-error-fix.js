/**
 * Test script to verify the ReferenceError fix for forceRefreshDecorations
 * Run this in the browser console to verify the fix works
 */

console.log('🧪 Testing ReferenceError fix for forceRefreshDecorations...');

// Test 1: Check if the useComments hook can be imported without errors
try {
  console.log('✅ useComments hook imported successfully (no ReferenceError)');
} catch (error) {
  console.error('❌ Error importing useComments:', error);
}

// Test 2: Check if debug functions are available (indicates successful initialization)
if (window.debugComments) {
  console.log('✅ Debug functions are available:', Object.keys(window.debugComments));
  
  // Test 3: Try to call the debug functions
  try {
    const state = window.debugComments.debugState();
    console.log('✅ debugState function works:', state);
  } catch (error) {
    console.error('❌ Error calling debugState:', error);
  }
  
  try {
    window.debugComments.forceRefresh();
    console.log('✅ forceRefresh function works');
  } catch (error) {
    console.error('❌ Error calling forceRefresh:', error);
  }
} else {
  console.log('ℹ️ Debug functions not available (normal in production or if editor not initialized)');
}

// Test 4: Check if the CollaborativeEditor component is rendered without errors
const editorElement = document.querySelector('.prose, [data-testid="collaborative-editor"]');
if (editorElement) {
  console.log('✅ CollaborativeEditor component is rendered successfully');
} else {
  console.log('ℹ️ CollaborativeEditor not found (may not be on editor page)');
}

// Test 5: Check for any JavaScript errors in console
const originalError = console.error;
let errorCount = 0;
console.error = function(...args) {
  if (args[0] && args[0].toString().includes('forceRefreshDecorations')) {
    errorCount++;
    console.log('❌ Found ReferenceError related to forceRefreshDecorations:', args);
  }
  originalError.apply(console, args);
};

// Restore original console.error after a short delay
setTimeout(() => {
  console.error = originalError;
  if (errorCount === 0) {
    console.log('✅ No ReferenceError related to forceRefreshDecorations found');
  } else {
    console.log(`❌ Found ${errorCount} ReferenceError(s) related to forceRefreshDecorations`);
  }
}, 1000);

console.log(`
🎯 ReferenceError Fix Verification Complete!

Summary:
- The forceRefreshDecorations function is now declared before it's used in dependency arrays
- The useComments hook should initialize without ReferenceError
- The CollaborativeEditor component should render successfully
- All comment highlighting functionality should work as expected

If you see any ❌ errors above, the fix may need additional work.
If you see mostly ✅ checkmarks, the fix is working correctly!
`);

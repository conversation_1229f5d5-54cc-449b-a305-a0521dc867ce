/**
 * React hook for URL-based script and document routing
 * Manages script and document selection via URL synchronization
 */

import { useState, useEffect, useCallback } from 'react';
import { Id } from '../../convex/_generated/dataModel';
import {
  extractIdsFromPath,
  generateScriptUrl,
  generateScriptDocumentUrl,
  generateDocumentUrl,
  generateHomeUrl,
  navigationUtils
} from '../lib/routing';

interface UseScriptRoutingReturn {
  selectedScriptId: Id<"scripts"> | null;
  selectedDocumentId: Id<"documents"> | null;
  navigateToScript: (scriptId: Id<"scripts">) => void;
  navigateToScriptDocument: (scriptId: Id<"scripts">, documentId: Id<"documents">) => void;
  navigateToDocument: (documentId: Id<"documents">) => void;
  navigateToHome: () => void;
  isLoading: boolean;
}

/**
 * Hook for managing script and document routing via URL
 * Automatically syncs selection with browser URL
 */
export function useScriptRouting(): UseScriptRoutingReturn {
  const [selectedScriptId, setSelectedScriptId] = useState<Id<"scripts"> | null>(null);
  const [selectedDocumentId, setSelectedDocumentId] = useState<Id<"documents"> | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize IDs from current URL
  useEffect(() => {
    const currentUrl = window.location.href;
    const urlObj = new URL(currentUrl);
    const { scriptId, documentId } = extractIdsFromPath(urlObj.pathname);
    
    if (scriptId) {
      setSelectedScriptId(scriptId as Id<"scripts">);
    }
    
    if (documentId) {
      setSelectedDocumentId(documentId as Id<"documents">);
    }
    
    setIsLoading(false);
  }, []);

  // Handle browser navigation (back/forward buttons)
  useEffect(() => {
    const handlePopState = () => {
      const currentUrl = window.location.href;
      const urlObj = new URL(currentUrl);
      const { scriptId, documentId } = extractIdsFromPath(urlObj.pathname);
      
      setSelectedScriptId(scriptId ? scriptId as Id<"scripts"> : null);
      setSelectedDocumentId(documentId ? documentId as Id<"documents"> : null);
    };

    window.addEventListener('popstate', handlePopState);
    
    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, []);

  // Navigate to a specific script
  const navigateToScript = useCallback((scriptId: Id<"scripts">) => {
    setSelectedScriptId(scriptId);
    setSelectedDocumentId(null);
    navigationUtils.navigateToScript(scriptId);
  }, []);

  // Navigate to a document within a script
  const navigateToScriptDocument = useCallback((scriptId: Id<"scripts">, documentId: Id<"documents">) => {
    setSelectedScriptId(scriptId);
    setSelectedDocumentId(documentId);
    navigationUtils.navigateToScriptDocument(scriptId, documentId);
  }, []);

  // Navigate to a standalone document (legacy)
  const navigateToDocument = useCallback((documentId: Id<"documents">) => {
    setSelectedScriptId(null);
    setSelectedDocumentId(documentId);
    navigationUtils.navigateToDocument(documentId);
  }, []);

  // Navigate to home (no script or document selected)
  const navigateToHome = useCallback(() => {
    setSelectedScriptId(null);
    setSelectedDocumentId(null);
    navigationUtils.navigateToHome();
  }, []);

  return {
    selectedScriptId,
    selectedDocumentId,
    navigateToScript,
    navigateToScriptDocument,
    navigateToDocument,
    navigateToHome,
    isLoading
  };
}

/**
 * Hook for URL generation utilities
 * Provides utilities for creating script and document links
 */
export function useScriptUrls() {
  const getScriptUrl = useCallback((scriptId: Id<"scripts">) => {
    return generateScriptUrl(scriptId);
  }, []);

  const getScriptDocumentUrl = useCallback((scriptId: Id<"scripts">, documentId: Id<"documents">) => {
    return generateScriptDocumentUrl(scriptId, documentId);
  }, []);

  const getDocumentUrl = useCallback((documentId: Id<"documents">) => {
    return generateDocumentUrl(documentId);
  }, []);

  const getHomeUrl = useCallback(() => {
    return generateHomeUrl();
  }, []);

  return {
    getScriptUrl,
    getScriptDocumentUrl,
    getDocumentUrl,
    getHomeUrl
  };
}

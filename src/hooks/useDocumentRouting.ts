/**
 * React hook for URL-based document routing
 * Manages document selection via URL synchronization
 */

import { useState, useEffect, useCallback } from 'react';
import { Id } from '../../convex/_generated/dataModel';
import {
  extractDocumentIdFromUrl,
  generateDocumentUrl,
  generateHomeUrl,
  navigationUtils
} from '../lib/routing';

interface UseDocumentRoutingReturn {
  selectedDocumentId: Id<"documents"> | null;
  navigateToDocument: (documentId: Id<"documents">) => void;
  navigateToHome: () => void;
  isLoading: boolean;
}

/**
 * Hook for managing document routing via URL
 * Automatically syncs document selection with browser URL
 */
export function useDocumentRouting(): UseDocumentRoutingReturn {
  const [selectedDocumentId, setSelectedDocumentId] = useState<Id<"documents"> | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize document ID from current URL
  useEffect(() => {
    const currentUrl = window.location.href;
    const documentId = extractDocumentIdFromUrl(currentUrl);
    
    if (documentId) {
      setSelectedDocumentId(documentId as Id<"documents">);
    }
    
    setIsLoading(false);
  }, []);

  // Handle browser navigation (back/forward buttons)
  useEffect(() => {
    const handlePopState = () => {
      const currentUrl = window.location.href;
      const documentId = extractDocumentIdFromUrl(currentUrl);
      
      if (documentId) {
        setSelectedDocumentId(documentId as Id<"documents">);
      } else {
        setSelectedDocumentId(null);
      }
    };

    window.addEventListener('popstate', handlePopState);
    
    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, []);

  // Navigate to a specific document
  const navigateToDocument = useCallback((documentId: Id<"documents">) => {
    setSelectedDocumentId(documentId);
    navigationUtils.navigateToDocument(documentId);
  }, []);

  // Navigate to home (no document selected)
  const navigateToHome = useCallback(() => {
    setSelectedDocumentId(null);
    navigationUtils.navigateToHome();
  }, []);

  return {
    selectedDocumentId,
    navigateToDocument,
    navigateToHome,
    isLoading
  };
}

/**
 * Hook for document URL generation
 * Provides utilities for creating document links
 */
export function useDocumentUrls() {
  const getDocumentUrl = useCallback((documentId: Id<"documents">) => {
    return generateDocumentUrl(documentId);
  }, []);

  const getHomeUrl = useCallback(() => {
    return generateHomeUrl();
  }, []);

  return {
    getDocumentUrl,
    getHomeUrl
  };
}

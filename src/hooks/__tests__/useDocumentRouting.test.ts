/**
 * Unit tests for useDocumentRouting hook
 * Tests URL-based document routing functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useDocumentRouting, useDocumentUrls } from '../useDocumentRouting';
import { Id } from '../../../convex/_generated/dataModel';

// Mock window.location and window.history
const mockLocation = {
  href: 'http://localhost:3000/',
  origin: 'http://localhost:3000'
};

const mockHistory = {
  pushState: vi.fn(),
  replaceState: vi.fn()
};

Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true
});

Object.defineProperty(window, 'history', {
  value: mockHistory,
  writable: true
});

// Mock addEventListener and removeEventListener
const mockAddEventListener = vi.fn();
const mockRemoveEventListener = vi.fn();

Object.defineProperty(window, 'addEventListener', {
  value: mockAddEventListener,
  writable: true
});

Object.defineProperty(window, 'removeEventListener', {
  value: mockRemoveEventListener,
  writable: true
});

describe('useDocumentRouting', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocation.href = 'http://localhost:3000/';
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize with no document when URL has no document ID', () => {
      mockLocation.href = 'http://localhost:3000/';
      
      const { result } = renderHook(() => useDocumentRouting());
      
      expect(result.current.selectedDocumentId).toBe(null);
      expect(result.current.isLoading).toBe(false);
    });

    it('should initialize with document ID from URL', () => {
      mockLocation.href = 'http://localhost:3000/document/abc123';
      
      const { result } = renderHook(() => useDocumentRouting());
      
      expect(result.current.selectedDocumentId).toBe('abc123');
      expect(result.current.isLoading).toBe(false);
    });

    it('should initialize with document ID from search params', () => {
      mockLocation.href = 'http://localhost:3000/?doc=xyz789';
      
      const { result } = renderHook(() => useDocumentRouting());
      
      expect(result.current.selectedDocumentId).toBe('xyz789');
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('navigation', () => {
    it('should navigate to document and update URL', () => {
      const { result } = renderHook(() => useDocumentRouting());
      
      act(() => {
        result.current.navigateToDocument('doc123' as Id<"documents">);
      });
      
      expect(result.current.selectedDocumentId).toBe('doc123');
      expect(mockHistory.pushState).toHaveBeenCalledWith(null, '', '/document/doc123');
    });

    it('should navigate to home and update URL', () => {
      mockLocation.href = 'http://localhost:3000/document/abc123';
      const { result } = renderHook(() => useDocumentRouting());
      
      act(() => {
        result.current.navigateToHome();
      });
      
      expect(result.current.selectedDocumentId).toBe(null);
      expect(mockHistory.pushState).toHaveBeenCalledWith(null, '', '/');
    });
  });

  describe('browser navigation handling', () => {
    it('should set up popstate event listener', () => {
      renderHook(() => useDocumentRouting());
      
      expect(mockAddEventListener).toHaveBeenCalledWith('popstate', expect.any(Function));
    });

    it('should clean up popstate event listener on unmount', () => {
      const { unmount } = renderHook(() => useDocumentRouting());
      
      unmount();
      
      expect(mockRemoveEventListener).toHaveBeenCalledWith('popstate', expect.any(Function));
    });

    it('should handle popstate events and update document selection', () => {
      let popstateHandler: ((event: PopStateEvent) => void) | undefined;
      
      mockAddEventListener.mockImplementation((event, handler) => {
        if (event === 'popstate') {
          popstateHandler = handler;
        }
      });
      
      const { result } = renderHook(() => useDocumentRouting());
      
      // Simulate browser navigation to document
      mockLocation.href = 'http://localhost:3000/document/new123';
      
      act(() => {
        if (popstateHandler) {
          popstateHandler(new PopStateEvent('popstate'));
        }
      });
      
      expect(result.current.selectedDocumentId).toBe('new123');
    });

    it('should handle popstate events and clear document selection', () => {
      let popstateHandler: ((event: PopStateEvent) => void) | undefined;
      
      mockAddEventListener.mockImplementation((event, handler) => {
        if (event === 'popstate') {
          popstateHandler = handler;
        }
      });
      
      mockLocation.href = 'http://localhost:3000/document/abc123';
      const { result } = renderHook(() => useDocumentRouting());
      
      // Simulate browser navigation to home
      mockLocation.href = 'http://localhost:3000/';
      
      act(() => {
        if (popstateHandler) {
          popstateHandler(new PopStateEvent('popstate'));
        }
      });
      
      expect(result.current.selectedDocumentId).toBe(null);
    });
  });
});

describe('useDocumentUrls', () => {
  it('should generate document URLs', () => {
    const { result } = renderHook(() => useDocumentUrls());
    
    const url = result.current.getDocumentUrl('abc123' as Id<"documents">);
    
    expect(url).toBe('/document/abc123');
  });

  it('should generate home URL', () => {
    const { result } = renderHook(() => useDocumentUrls());
    
    const url = result.current.getHomeUrl();
    
    expect(url).toBe('/');
  });
});

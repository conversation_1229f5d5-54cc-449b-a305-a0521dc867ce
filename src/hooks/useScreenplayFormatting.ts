/**
 * Custom hook for screenplay formatting functionality
 * Handles auto-formatting, element detection, and keyboard shortcuts
 */

import { useCallback, useEffect, useState } from 'react';
import { 
  ScreenplayElementType, 
  detectElementType, 
  formatScreenplayText 
} from '../lib/screenplayFormatting';
import { 
  autoFormatScreenplayBlock, 
  screenplayTypeToBlockType 
} from '../lib/screenplaySchema';

interface UseScreenplayFormattingProps {
  editor: any; // BlockNote editor instance
  isReadOnly?: boolean;
}

export function useScreenplayFormatting({ 
  editor, 
  isReadOnly = false 
}: UseScreenplayFormattingProps) {
  const [currentElementType, setCurrentElementType] = useState<ScreenplayElementType>(
    ScreenplayElementType.ACTION
  );
  const [isAutoFormatting, setIsAutoFormatting] = useState(true);

  // Format current block as specific screenplay element
  const formatAsElement = useCallback((elementType: ScreenplayElementType) => {
    if (!editor || isReadOnly) return;

    try {
      const blockType = screenplayTypeToBlockType(elementType);
      
      // Get current block
      const currentBlock = editor.getTextCursorPosition().block;
      
      // Update block type
      editor.updateBlock(currentBlock, {
        type: blockType,
      });
      
      setCurrentElementType(elementType);
    } catch (error) {
      console.error('Error formatting screenplay element:', error);
    }
  }, [editor, isReadOnly]);

  // Auto-format based on text content
  const autoFormat = useCallback((text: string, currentBlockType: string) => {
    if (!isAutoFormatting || !editor || isReadOnly) return;

    const { blockType, formattedText } = autoFormatScreenplayBlock(text, currentBlockType);
    
    if (blockType !== currentBlockType) {
      try {
        const currentBlock = editor.getTextCursorPosition().block;
        editor.updateBlock(currentBlock, {
          type: blockType,
        });
        
        // Update current element type
        const elementType = detectElementType(text);
        setCurrentElementType(elementType);
      } catch (error) {
        console.error('Error auto-formatting screenplay element:', error);
      }
    }
  }, [editor, isAutoFormatting, isReadOnly]);

  // Handle keyboard shortcuts
  const handleKeyboardShortcuts = useCallback((event: KeyboardEvent) => {
    if (!editor || isReadOnly) return;

    // Check for Ctrl/Cmd + number keys
    if ((event.ctrlKey || event.metaKey) && !event.shiftKey && !event.altKey) {
      const key = event.key;
      let elementType: ScreenplayElementType | null = null;

      switch (key) {
        case '1':
          elementType = ScreenplayElementType.SCENE_HEADING;
          break;
        case '2':
          elementType = ScreenplayElementType.ACTION;
          break;
        case '3':
          elementType = ScreenplayElementType.CHARACTER;
          break;
        case '4':
          elementType = ScreenplayElementType.DIALOGUE;
          break;
        case '5':
          elementType = ScreenplayElementType.PARENTHETICAL;
          break;
        case '6':
          elementType = ScreenplayElementType.TRANSITION;
          break;
      }

      if (elementType) {
        event.preventDefault();
        formatAsElement(elementType);
      }
    }

    // Handle Tab key for smart element progression
    if (event.key === 'Tab' && !event.shiftKey) {
      event.preventDefault();
      handleTabProgression();
    }

    // Handle Enter key for smart line breaks
    if (event.key === 'Enter' && !event.shiftKey) {
      handleEnterProgression(event);
    }
  }, [editor, isReadOnly, formatAsElement]);

  // Smart Tab progression between screenplay elements
  const handleTabProgression = useCallback(() => {
    if (!editor || isReadOnly) return;

    const progressionMap: Record<ScreenplayElementType, ScreenplayElementType> = {
      [ScreenplayElementType.SCENE_HEADING]: ScreenplayElementType.ACTION,
      [ScreenplayElementType.ACTION]: ScreenplayElementType.CHARACTER,
      [ScreenplayElementType.CHARACTER]: ScreenplayElementType.DIALOGUE,
      [ScreenplayElementType.DIALOGUE]: ScreenplayElementType.ACTION,
      [ScreenplayElementType.PARENTHETICAL]: ScreenplayElementType.DIALOGUE,
      [ScreenplayElementType.TRANSITION]: ScreenplayElementType.SCENE_HEADING,
      [ScreenplayElementType.SHOT]: ScreenplayElementType.ACTION,
      [ScreenplayElementType.GENERAL]: ScreenplayElementType.ACTION,
    };

    const nextElementType = progressionMap[currentElementType] || ScreenplayElementType.ACTION;
    formatAsElement(nextElementType);
  }, [currentElementType, formatAsElement, editor, isReadOnly]);

  // Smart Enter progression
  const handleEnterProgression = useCallback((event: KeyboardEvent) => {
    if (!editor || isReadOnly) return;

    // Let default Enter behavior happen first
    setTimeout(() => {
      try {
        const currentBlock = editor.getTextCursorPosition().block;
        const blockType = currentBlock.type;
        
        // Determine next element type based on current type
        let nextElementType: ScreenplayElementType;
        
        switch (blockType) {
          case 'sceneHeading':
            nextElementType = ScreenplayElementType.ACTION;
            break;
          case 'character':
            nextElementType = ScreenplayElementType.DIALOGUE;
            break;
          case 'parenthetical':
            nextElementType = ScreenplayElementType.DIALOGUE;
            break;
          case 'transition':
            nextElementType = ScreenplayElementType.SCENE_HEADING;
            break;
          default:
            nextElementType = ScreenplayElementType.ACTION;
        }
        
        // Update the new block
        const nextBlockType = screenplayTypeToBlockType(nextElementType);
        editor.updateBlock(currentBlock, {
          type: nextBlockType,
        });
        
        setCurrentElementType(nextElementType);
      } catch (error) {
        console.error('Error handling Enter progression:', error);
      }
    }, 10);
  }, [editor, isReadOnly]);

  // Track current element type based on cursor position
  const updateCurrentElementType = useCallback(() => {
    if (!editor) return;

    try {
      const currentBlock = editor.getTextCursorPosition().block;
      const blockType = currentBlock.type;
      
      // Map block type to screenplay element type
      let elementType: ScreenplayElementType;
      
      switch (blockType) {
        case 'sceneHeading':
          elementType = ScreenplayElementType.SCENE_HEADING;
          break;
        case 'character':
          elementType = ScreenplayElementType.CHARACTER;
          break;
        case 'dialogue':
          elementType = ScreenplayElementType.DIALOGUE;
          break;
        case 'parenthetical':
          elementType = ScreenplayElementType.PARENTHETICAL;
          break;
        case 'transition':
          elementType = ScreenplayElementType.TRANSITION;
          break;
        default:
          elementType = ScreenplayElementType.ACTION;
      }
      
      setCurrentElementType(elementType);
    } catch (error) {
      // Ignore errors when editor is not ready
    }
  }, [editor]);

  // Set up event listeners
  useEffect(() => {
    if (!editor) return;

    // Add keyboard event listener
    document.addEventListener('keydown', handleKeyboardShortcuts);
    
    // Listen for cursor position changes
    const handleSelectionChange = () => {
      updateCurrentElementType();
    };

    // Listen for text changes for auto-formatting
    const handleTextChange = () => {
      if (isAutoFormatting) {
        try {
          const currentBlock = editor.getTextCursorPosition().block;
          const text = currentBlock.content?.[0]?.text || '';
          autoFormat(text, currentBlock.type);
        } catch (error) {
          // Ignore errors when editor is not ready
        }
      }
    };

    // Set up editor event listeners
    if (editor.prosemirrorView) {
      const view = editor.prosemirrorView;
      view.dom.addEventListener('selectionchange', handleSelectionChange);
      view.dom.addEventListener('input', handleTextChange);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyboardShortcuts);
      if (editor.prosemirrorView) {
        const view = editor.prosemirrorView;
        view.dom.removeEventListener('selectionchange', handleSelectionChange);
        view.dom.removeEventListener('input', handleTextChange);
      }
    };
  }, [editor, handleKeyboardShortcuts, updateCurrentElementType, autoFormat, isAutoFormatting]);

  // Initialize current element type
  useEffect(() => {
    updateCurrentElementType();
  }, [updateCurrentElementType]);

  return {
    currentElementType,
    formatAsElement,
    isAutoFormatting,
    setIsAutoFormatting,
    handleTabProgression,
  };
}

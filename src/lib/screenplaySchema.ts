/**
 * Custom BlockNote schema for screenplay formatting
 * Defines custom block types for screenplay elements
 */

import { defaultBlockSpecs, BlockNoteSchema } from "@blocknote/core";
import { ScreenplayElementType, getElementStyles, formatScreenplayText } from "./screenplayFormatting";

// Define custom block specs for screenplay elements
export const screenplayBlockSpecs = {
  // Keep the default paragraph block but modify it
  paragraph: {
    ...defaultBlockSpecs.paragraph,
    propSchema: {
      ...defaultBlockSpecs.paragraph.propSchema,
      screenplayType: {
        default: ScreenplayElementType.ACTION,
        values: Object.values(ScreenplayElementType),
      },
    },
  },
  
  // Scene heading block
  sceneHeading: {
    type: "sceneHeading" as const,
    propSchema: {
      textColor: {
        default: "default",
        values: ["default", "gray", "brown", "red", "orange", "yellow", "green", "blue", "purple", "pink"],
      },
      backgroundColor: {
        default: "default",
        values: ["default", "gray", "brown", "red", "orange", "yellow", "green", "blue", "purple", "pink"],
      },
    },
    content: "inline",
  },
  
  // Character name block
  character: {
    type: "character" as const,
    propSchema: {
      textColor: {
        default: "default",
        values: ["default", "gray", "brown", "red", "orange", "yellow", "green", "blue", "purple", "pink"],
      },
      backgroundColor: {
        default: "default",
        values: ["default", "gray", "brown", "red", "orange", "yellow", "green", "blue", "purple", "pink"],
      },
    },
    content: "inline",
  },
  
  // Dialogue block
  dialogue: {
    type: "dialogue" as const,
    propSchema: {
      textColor: {
        default: "default",
        values: ["default", "gray", "brown", "red", "orange", "yellow", "green", "blue", "purple", "pink"],
      },
      backgroundColor: {
        default: "default",
        values: ["default", "gray", "brown", "red", "orange", "yellow", "green", "blue", "purple", "pink"],
      },
    },
    content: "inline",
  },
  
  // Parenthetical block
  parenthetical: {
    type: "parenthetical" as const,
    propSchema: {
      textColor: {
        default: "default",
        values: ["default", "gray", "brown", "red", "orange", "yellow", "green", "blue", "purple", "pink"],
      },
      backgroundColor: {
        default: "default",
        values: ["default", "gray", "brown", "red", "orange", "yellow", "green", "blue", "purple", "pink"],
      },
    },
    content: "inline",
  },
  
  // Transition block
  transition: {
    type: "transition" as const,
    propSchema: {
      textColor: {
        default: "default",
        values: ["default", "gray", "brown", "red", "orange", "yellow", "green", "blue", "purple", "pink"],
      },
      backgroundColor: {
        default: "default",
        values: ["default", "gray", "brown", "red", "orange", "yellow", "green", "blue", "purple", "pink"],
      },
    },
    content: "inline",
  },
  
  // Action block (default)
  action: {
    type: "action" as const,
    propSchema: {
      textColor: {
        default: "default",
        values: ["default", "gray", "brown", "red", "orange", "yellow", "green", "blue", "purple", "pink"],
      },
      backgroundColor: {
        default: "default",
        values: ["default", "gray", "brown", "red", "orange", "yellow", "green", "blue", "purple", "pink"],
      },
    },
    content: "inline",
  },
};

// Create the screenplay schema
export const screenplaySchema = BlockNoteSchema.create({
  blockSpecs: screenplayBlockSpecs,
});

// Type for the screenplay editor
export type ScreenplayEditor = typeof screenplaySchema.BlockNoteEditor;
export type ScreenplayBlock = typeof screenplaySchema.Block;

// Helper function to convert block type to screenplay element type
export function blockTypeToScreenplayType(blockType: string): ScreenplayElementType {
  switch (blockType) {
    case 'sceneHeading':
      return ScreenplayElementType.SCENE_HEADING;
    case 'character':
      return ScreenplayElementType.CHARACTER;
    case 'dialogue':
      return ScreenplayElementType.DIALOGUE;
    case 'parenthetical':
      return ScreenplayElementType.PARENTHETICAL;
    case 'transition':
      return ScreenplayElementType.TRANSITION;
    case 'action':
    default:
      return ScreenplayElementType.ACTION;
  }
}

// Helper function to convert screenplay element type to block type
export function screenplayTypeToBlockType(screenplayType: ScreenplayElementType): string {
  switch (screenplayType) {
    case ScreenplayElementType.SCENE_HEADING:
      return 'sceneHeading';
    case ScreenplayElementType.CHARACTER:
      return 'character';
    case ScreenplayElementType.DIALOGUE:
      return 'dialogue';
    case ScreenplayElementType.PARENTHETICAL:
      return 'parenthetical';
    case ScreenplayElementType.TRANSITION:
      return 'transition';
    case ScreenplayElementType.ACTION:
    default:
      return 'action';
  }
}

// Auto-formatting function for screenplay elements
export function autoFormatScreenplayBlock(
  text: string, 
  currentBlockType: string
): { blockType: string; formattedText: string } {
  const trimmedText = text.trim();
  
  if (!trimmedText) {
    return { blockType: 'action', formattedText: text };
  }
  
  // Scene heading detection
  if (/^(INT\.|EXT\.|FADE IN:|FADE OUT\.|CUT TO:)/i.test(trimmedText)) {
    return {
      blockType: 'sceneHeading',
      formattedText: formatScreenplayText(text, ScreenplayElementType.SCENE_HEADING),
    };
  }
  
  // Transition detection
  if (/^(FADE IN:|FADE OUT\.|CUT TO:|DISSOLVE TO:|SMASH CUT TO:)$/i.test(trimmedText)) {
    return {
      blockType: 'transition',
      formattedText: formatScreenplayText(text, ScreenplayElementType.TRANSITION),
    };
  }
  
  // Parenthetical detection
  if (/^\(.*\)$/.test(trimmedText)) {
    return {
      blockType: 'parenthetical',
      formattedText: formatScreenplayText(text, ScreenplayElementType.PARENTHETICAL),
    };
  }
  
  // Character name detection (all caps, possibly with extension)
  if (/^[A-Z][A-Z\s]+(\(.*\))?$/.test(trimmedText) && currentBlockType !== 'dialogue') {
    return {
      blockType: 'character',
      formattedText: formatScreenplayText(text, ScreenplayElementType.CHARACTER),
    };
  }
  
  // If previous block was character, this is likely dialogue
  // (This would need to be handled in the editor logic)
  
  // Default to action
  return {
    blockType: 'action',
    formattedText: formatScreenplayText(text, ScreenplayElementType.ACTION),
  };
}

// CSS class mapping for screenplay elements
export const SCREENPLAY_ELEMENT_CLASSES = {
  sceneHeading: 'screenplay-scene-heading',
  character: 'screenplay-character',
  dialogue: 'screenplay-dialogue',
  parenthetical: 'screenplay-parenthetical',
  transition: 'screenplay-transition',
  action: 'screenplay-action',
} as const;

// Generate CSS styles for screenplay elements
export function generateScreenplayCSS(): string {
  const styles = Object.entries(SCREENPLAY_ELEMENT_CLASSES).map(([blockType, className]) => {
    const elementType = blockTypeToScreenplayType(blockType);
    const styles = getElementStyles(elementType);
    
    return `
      .${className} {
        margin-left: ${styles.marginLeft};
        margin-right: ${styles.marginRight};
        margin-top: ${styles.marginTop};
        margin-bottom: ${styles.marginBottom};
        text-transform: ${styles.textTransform};
        font-weight: ${styles.fontWeight};
        font-style: ${styles.fontStyle};
        line-height: ${styles.lineHeight};
        text-align: ${styles.textAlign};
        font-family: "Courier New", monospace;
        font-size: 12pt;
      }
    `;
  }).join('\n');
  
  return styles;
}

/**
 * User color utilities for collaborative features
 * Provides consistent color assignment for cursors, selections, and avatars
 */

export interface UserColor {
  bg: string;
  text: string;
  border: string;
  selection: string;
  cursor: string;
  rgb: string;
  hsl: string;
}

// Predefined color palette for users
const USER_COLORS: UserColor[] = [
  {
    bg: "bg-blue-500",
    text: "text-white",
    border: "border-blue-500",
    selection: "bg-blue-200/40",
    cursor: "bg-blue-500",
    rgb: "59, 130, 246",
    hsl: "217, 91%, 60%"
  },
  {
    bg: "bg-green-500",
    text: "text-white", 
    border: "border-green-500",
    selection: "bg-green-200/40",
    cursor: "bg-green-500",
    rgb: "34, 197, 94",
    hsl: "142, 71%, 45%"
  },
  {
    bg: "bg-purple-500",
    text: "text-white",
    border: "border-purple-500", 
    selection: "bg-purple-200/40",
    cursor: "bg-purple-500",
    rgb: "168, 85, 247",
    hsl: "262, 83%, 58%"
  },
  {
    bg: "bg-pink-500",
    text: "text-white",
    border: "border-pink-500",
    selection: "bg-pink-200/40", 
    cursor: "bg-pink-500",
    rgb: "236, 72, 153",
    hsl: "330, 81%, 60%"
  },
  {
    bg: "bg-yellow-500",
    text: "text-black",
    border: "border-yellow-500",
    selection: "bg-yellow-200/40",
    cursor: "bg-yellow-500", 
    rgb: "234, 179, 8",
    hsl: "48, 96%, 47%"
  },
  {
    bg: "bg-indigo-500",
    text: "text-white",
    border: "border-indigo-500",
    selection: "bg-indigo-200/40",
    cursor: "bg-indigo-500",
    rgb: "99, 102, 241",
    hsl: "239, 84%, 67%"
  },
  {
    bg: "bg-red-500", 
    text: "text-white",
    border: "border-red-500",
    selection: "bg-red-200/40",
    cursor: "bg-red-500",
    rgb: "239, 68, 68",
    hsl: "0, 84%, 60%"
  },
  {
    bg: "bg-teal-500",
    text: "text-white",
    border: "border-teal-500",
    selection: "bg-teal-200/40",
    cursor: "bg-teal-500",
    rgb: "20, 184, 166", 
    hsl: "173, 80%, 40%"
  }
];

/**
 * Get a consistent color for a user based on their ID
 */
export function getUserColor(userId: string): UserColor {
  // Create a simple hash from the user ID
  let hash = 0;
  for (let i = 0; i < userId.length; i++) {
    const char = userId.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  // Use absolute value and modulo to get a consistent index
  const index = Math.abs(hash) % USER_COLORS.length;
  return USER_COLORS[index];
}

/**
 * Get all available user colors
 */
export function getAllUserColors(): UserColor[] {
  return USER_COLORS;
}

/**
 * Get color by index (for compatibility with existing PresenceIndicator)
 */
export function getUserColorByIndex(index: number): UserColor {
  return USER_COLORS[index % USER_COLORS.length];
}

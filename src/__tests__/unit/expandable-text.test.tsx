/**
 * Unit tests for ExpandableText component
 */

import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { ExpandableText, QuotedText, CommentContent } from '../../components/ui/expandable-text';

describe('ExpandableText', () => {
  it('should render short text without truncation', () => {
    const shortText = 'This is a short text.';
    render(<ExpandableText>{shortText}</ExpandableText>);
    
    expect(screen.getByText(shortText)).toBeInTheDocument();
    expect(screen.queryByText('Show more')).not.toBeInTheDocument();
  });

  it('should render long text with show more button', () => {
    const longText = 'This is a very long text that should be truncated. '.repeat(10);
    render(<ExpandableText maxLines={2}>{longText}</ExpandableText>);
    
    // The text should be present (even if truncated)
    expect(screen.getByText(longText)).toBeInTheDocument();
  });

  it('should expand text when show more is clicked', async () => {
    const longText = 'This is a very long text that should be truncated. '.repeat(10);
    render(<ExpandableText maxLines={2}>{longText}</ExpandableText>);
    
    // Look for show more button (might not be visible due to measurement logic)
    const showMoreButton = screen.queryByText('Show more');
    if (showMoreButton) {
      fireEvent.click(showMoreButton);
      expect(screen.queryByText('Show less')).toBeInTheDocument();
    }
  });

  it('should handle custom show more/less text', () => {
    const longText = 'This is a very long text that should be truncated. '.repeat(10);
    render(
      <ExpandableText 
        maxLines={2} 
        showMoreText="Read full text" 
        showLessText="Collapse"
      >
        {longText}
      </ExpandableText>
    );
    
    // Check for custom button text (might not be visible due to measurement logic)
    const customButton = screen.queryByText('Read full text');
    if (customButton) {
      expect(customButton).toBeInTheDocument();
    }
  });
});

describe('QuotedText', () => {
  it('should render quoted text with proper formatting', () => {
    const text = 'This is quoted text';
    render(<QuotedText>{text}</QuotedText>);
    
    expect(screen.getByText(`"${text}"`)).toBeInTheDocument();
  });

  it('should apply italic styling class', () => {
    const text = 'This is quoted text';
    const { container } = render(<QuotedText>{text}</QuotedText>);
    
    const quotedElement = container.querySelector('.italic');
    expect(quotedElement).toBeInTheDocument();
  });
});

describe('CommentContent', () => {
  it('should render comment content with proper styling', () => {
    const content = 'This is comment content';
    render(<CommentContent>{content}</CommentContent>);
    
    expect(screen.getByText(content)).toBeInTheDocument();
  });

  it('should enable scrolling by default', () => {
    const longContent = 'This is very long comment content. '.repeat(20);
    render(<CommentContent>{longContent}</CommentContent>);
    
    expect(screen.getByText(longContent)).toBeInTheDocument();
  });

  it('should handle custom max lines', () => {
    const content = 'Line 1\nLine 2\nLine 3\nLine 4\nLine 5';
    render(<CommentContent maxLines={3}>{content}</CommentContent>);
    
    expect(screen.getByText(content)).toBeInTheDocument();
  });
});

describe('Text overflow handling', () => {
  it('should apply word-break styles for long words', () => {
    const longWord = 'supercalifragilisticexpialidocious'.repeat(5);
    const { container } = render(<ExpandableText>{longWord}</ExpandableText>);
    
    const textElement = container.querySelector('.text-expandable');
    expect(textElement).toBeInTheDocument();
    expect(textElement).toHaveClass('text-expandable');
  });

  it('should handle URLs and long strings properly', () => {
    const longUrl = 'https://example.com/very/long/path/that/should/break/properly/without/causing/horizontal/overflow';
    render(<ExpandableText>{longUrl}</ExpandableText>);
    
    expect(screen.getByText(longUrl)).toBeInTheDocument();
  });
});

/**
 * Integration tests for seamless document creation workflow
 * Tests instant document creation when users click "New Document" button
 */

import { test, expect } from '@playwright/test';

test.describe('Seamless Document Creation', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('/');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
  });

  test('should create document with custom name when using dialog', async ({ page }) => {
    // Sign in using anonymous auth (which should have create permissions in test environment)
    await page.locator('button:has-text("Sign in anonymously")').click();

    // Wait for authentication to complete
    await expect(page.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });

    // Wait for the document list to be visible
    await page.waitForSelector('text=My Documents', { timeout: 10000 });

    // Click the "New Document" button
    await page.click('button:has-text("New Document")');

    // Should open the create dialog
    await expect(page.locator('text=Create New Document')).toBeVisible({ timeout: 5000 });

    // Enter a custom document name
    await page.fill('input[placeholder="Enter document name..."]', 'My Test Document');

    // Click create button
    await page.click('button:has-text("Create Document")');

    // Should automatically navigate to the new document
    await page.waitForURL(/\/document\/.*/, { timeout: 10000 });

    // Should show the document editor
    await expect(page.locator('[data-testid="collaborative-editor"]')).toBeVisible({ timeout: 5000 });

    // Should show the custom title
    await expect(page.locator('text=My Test Document')).toBeVisible();

    // Should show the document in the sidebar as selected
    await expect(page.locator('.border-blue-500')).toBeVisible();
  });

  test('should not show New Document button for users without create permissions', async ({ page }) => {
    // For this test, we would need a way to simulate a user without create permissions
    // Since anonymous users in the test environment might have create permissions,
    // this test would need to be adjusted based on the actual permission setup

    // Sign in using anonymous auth
    await page.locator('button:has-text("Sign in anonymously")').click();

    // Wait for authentication to complete
    await expect(page.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });

    // Wait for the document list to be visible
    await page.waitForSelector('text=My Documents', { timeout: 10000 });

    // If the user doesn't have create permissions, should not show New Document button
    // This test might need adjustment based on actual permission configuration
    const hasCreateButton = await page.locator('button:has-text("New Document")').isVisible({ timeout: 2000 });

    if (!hasCreateButton) {
      // Should show limited access warning for users without create permissions
      await expect(page.locator('text=Limited Access')).toBeVisible();
    }
  });

  test('should handle document creation errors gracefully', async ({ page }) => {
    // This test would require mocking the backend to simulate creation failures
    // For now, we'll test that the dialog behaves correctly during creation

    // Sign in
    await page.locator('button:has-text("Sign in anonymously")').click();
    await expect(page.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });

    // Wait for the document list to be visible
    await page.waitForSelector('text=My Documents', { timeout: 10000 });

    // Click the "New Document" button
    await page.click('button:has-text("New Document")');

    // Enter document name
    await page.fill('input[placeholder="Enter document name..."]', 'Test Document');

    // Click create
    await page.click('button:has-text("Create Document")');

    // Should either succeed (navigate to document) or show error toast
    await page.waitForTimeout(5000);

    const hasDocument = await page.locator('[data-testid="collaborative-editor"]').isVisible();
    const hasErrorToast = await page.locator('[role="alert"]').isVisible();

    // Should either succeed or show error, not hang indefinitely
    expect(hasDocument || hasErrorToast).toBeTruthy();
  });

  test('should maintain URL routing with seamlessly created documents', async ({ page }) => {
    // Sign in
    await page.locator('button:has-text("Sign in anonymously")').click();
    await expect(page.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });

    // Wait for the document list to be visible
    await page.waitForSelector('text=My Documents', { timeout: 10000 });

    // Click the "New Document" button
    await page.click('button:has-text("New Document")');

    // Wait for navigation to document
    await page.waitForURL(/\/document\/.*/, { timeout: 10000 });

    const documentUrl = page.url();

    // Navigate away to home
    await page.goto('/');
    await page.waitForSelector('text=My Documents');

    // Navigate back to the document URL
    await page.goto(documentUrl);

    // Should load the document correctly
    await expect(page.locator('[data-testid="collaborative-editor"]')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('text=Untitled Document')).toBeVisible();
  });

  test('should show appropriate loading states during document creation', async ({ page }) => {
    // Sign in
    await page.locator('button:has-text("Sign in anonymously")').click();
    await expect(page.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });

    // Wait for the document list to be visible
    await page.waitForSelector('text=My Documents', { timeout: 10000 });

    // Click the "New Document" button
    await page.click('button:has-text("New Document")');

    // Should show creating state on the button (briefly)
    const creatingButtonVisible = await page.locator('button:has-text("Creating...")').isVisible({ timeout: 1000 });

    if (creatingButtonVisible) {
      // Button should be disabled during creation
      await expect(page.locator('button:has-text("Creating...")')).toBeDisabled();
    }

    // Should eventually transition to document view
    await expect(page.locator('[data-testid="collaborative-editor"]')).toBeVisible({ timeout: 10000 });
  });

  test('should show New Document button and dialog for custom naming', async ({ page }) => {
    // Sign in
    await page.locator('button:has-text("Sign in anonymously")').click();
    await expect(page.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });

    // Wait for the document list to be visible
    await page.waitForSelector('text=My Documents', { timeout: 10000 });

    // Should show "New Document" button in sidebar for users with create permissions
    await expect(page.locator('button:has-text("New Document")')).toBeVisible();

    // Click the button to open dialog
    await page.click('button:has-text("New Document")');

    // Should show create dialog for custom naming
    await expect(page.locator('text=Create New Document')).toBeVisible();
    await expect(page.locator('input[placeholder="Enter document name..."]')).toBeVisible();
  });

  test('should handle browser refresh on seamlessly created document', async ({ page }) => {
    // Sign in
    await page.locator('button:has-text("Sign in anonymously")').click();
    await expect(page.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });

    // Wait for the document list to be visible
    await page.waitForSelector('text=My Documents', { timeout: 10000 });

    // Click the "New Document" button
    await page.click('button:has-text("New Document")');

    // Wait for navigation to document
    await page.waitForURL(/\/document\/.*/, { timeout: 10000 });

    // Refresh the page
    await page.reload();

    // Should still load the document correctly after refresh
    await expect(page.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });
    await expect(page.locator('[data-testid="collaborative-editor"]')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('text=Untitled Document')).toBeVisible();
  });
});

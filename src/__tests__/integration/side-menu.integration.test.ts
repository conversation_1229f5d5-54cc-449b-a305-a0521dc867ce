/**
 * Integration tests for BlockNote side menu functionality
 * Tests the content node context selection tools (plus button, drag handle, etc.)
 */

import { test, expect } from '@playwright/test';

test.describe('Side Menu Content Node Tools', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Create a new document to test with
    await page.click('button:has-text("New Document")');
    await page.waitForSelector('.prose', { timeout: 10000 });
  });

  test('should display side menu tools when hovering over content blocks', async ({ page }) => {
    const editor = page.locator('.prose');
    await editor.click();
    
    // Add some content to create a block
    await page.keyboard.type('This is a test paragraph for side menu functionality.');
    await page.waitForTimeout(500);
    
    // Look for the side menu elements that should appear on hover
    // BlockNote typically shows side menu on the left side of blocks
    const sideMenuElements = page.locator('[data-testid*="side-menu"], .bn-side-menu, [class*="side-menu"]');
    
    // Check if any side menu related elements are present
    const sideMenuCount = await sideMenuElements.count();
    console.log(`Found ${sideMenuCount} side menu elements`);
    
    // If we don't find specific test IDs, look for common BlockNote side menu patterns
    const addButton = page.locator('button:has-text("+"), [aria-label*="Add"], [title*="Add"]');
    const dragHandle = page.locator('[aria-label*="Drag"], [title*="Drag"], button:has-text("⠿")');
    
    // Check if add button exists
    const addButtonCount = await addButton.count();
    console.log(`Found ${addButtonCount} add buttons`);
    
    // Check if drag handle exists  
    const dragHandleCount = await dragHandle.count();
    console.log(`Found ${dragHandleCount} drag handles`);
    
    // At least one of these should be present for a functioning side menu
    expect(sideMenuCount + addButtonCount + dragHandleCount).toBeGreaterThan(0);
  });

  test('should show plus button for adding new blocks', async ({ page }) => {
    const editor = page.locator('.prose');
    await editor.click();
    
    // Add content
    await page.keyboard.type('Test content for plus button functionality.');
    await page.waitForTimeout(500);
    
    // Look for plus button or add block functionality
    const plusButtons = page.locator('button:has-text("+"), [aria-label*="Add block"], [title*="Add block"]');
    
    // Check if we can find any plus/add buttons
    const count = await plusButtons.count();
    console.log(`Found ${count} plus/add buttons`);
    
    if (count > 0) {
      // Try to click the first plus button
      await plusButtons.first().click();
      await page.waitForTimeout(500);
      
      // Check if a menu or options appeared
      const menus = page.locator('[role="menu"], .dropdown, [class*="menu"]');
      const menuCount = await menus.count();
      console.log(`Found ${menuCount} menus after clicking plus button`);
    }
    
    // The test passes if we found at least one plus button
    expect(count).toBeGreaterThan(0);
  });

  test('should show drag handle for block manipulation', async ({ page }) => {
    const editor = page.locator('.prose');
    await editor.click();
    
    // Add content
    await page.keyboard.type('Test content for drag handle functionality.');
    await page.waitForTimeout(500);
    
    // Look for drag handle (often represented by ⠿ or similar)
    const dragHandles = page.locator('button:has-text("⠿"), [aria-label*="Drag"], [title*="Drag"], [class*="drag"]');
    
    const count = await dragHandles.count();
    console.log(`Found ${count} drag handles`);
    
    if (count > 0) {
      // Try to click the drag handle to see if it opens a menu
      await dragHandles.first().click();
      await page.waitForTimeout(500);
      
      // Look for context menu options
      const contextMenus = page.locator('[role="menu"], .context-menu, [class*="menu"]');
      const menuCount = await contextMenus.count();
      console.log(`Found ${menuCount} context menus after clicking drag handle`);
    }
    
    // The test passes if we found at least one drag handle
    expect(count).toBeGreaterThan(0);
  });

  test('should provide block manipulation options', async ({ page }) => {
    const editor = page.locator('.prose');
    await editor.click();
    
    // Add content
    await page.keyboard.type('Test content for block manipulation options.');
    await page.waitForTimeout(500);
    
    // Look for any block manipulation UI elements
    const manipulationElements = page.locator(`
      button:has-text("+"),
      button:has-text("⠿"),
      [aria-label*="Add"],
      [aria-label*="Drag"],
      [aria-label*="Delete"],
      [aria-label*="Move"],
      [title*="Add"],
      [title*="Drag"],
      [title*="Delete"],
      [title*="Move"],
      [class*="side-menu"],
      [class*="block-menu"]
    `);
    
    const count = await manipulationElements.count();
    console.log(`Found ${count} block manipulation elements`);
    
    // Log what we found for debugging
    for (let i = 0; i < Math.min(count, 5); i++) {
      const element = manipulationElements.nth(i);
      const text = await element.textContent();
      const ariaLabel = await element.getAttribute('aria-label');
      const title = await element.getAttribute('title');
      console.log(`Element ${i}: text="${text}", aria-label="${ariaLabel}", title="${title}"`);
    }
    
    // We should find at least some block manipulation elements
    expect(count).toBeGreaterThan(0);
  });

  test('should not show side menu tools for read-only users', async ({ page }) => {
    // This test would need a way to simulate read-only access
    // For now, we'll skip it or implement it when we have read-only test setup
    test.skip();
  });
});

/**
 * Playwright Integration Tests for Document Creation Workflow
 * Tests complete document creation workflow using browser automation
 * Verifies permission-based UI behavior and error handling
 */

import { test, expect } from '@playwright/test';

test.describe('Document Creation Integration Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');

    // Wait for the application to load
    await page.waitForLoadState('networkidle');
  });

  test('should display the application correctly', async ({ page }) => {
    // Basic smoke test to ensure the app loads
    await expect(page).toHaveTitle('Chef');

    // Check if main elements are present
    await expect(page.locator('body')).toBeVisible();

    // Check for the main header
    await expect(page.locator('h2:has-text("Collaborative Editor")')).toBeVisible();
  });

  test('should show sign-in form for unauthenticated users', async ({ page }) => {
    // Wait for the sign-in form to appear
    await expect(page.locator('form')).toBeVisible({ timeout: 10000 });

    // Check for sign-in form elements
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]:has-text("Sign in")')).toBeVisible();
    await expect(page.locator('button:has-text("Sign in anonymously")')).toBeVisible();
  });

  test('should handle anonymous sign-in workflow', async ({ page }) => {
    // Wait for the sign-in form to appear
    await expect(page.locator('form')).toBeVisible({ timeout: 10000 });

    // Click the anonymous sign-in button
    await page.locator('button:has-text("Sign in anonymously")').click();

    // Wait for authentication to complete and check for authenticated UI
    await expect(page.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });

    // Check that we can see the document list area (even if empty)
    await expect(page.locator('text=No documents yet').first()).toBeVisible({ timeout: 10000 });
  });

  test('should display loading states appropriately', async ({ page }) => {
    // Navigate to a fresh page to catch loading state
    await page.goto('/', { waitUntil: 'domcontentloaded' });

    // Check for initial loading spinner or loading text quickly
    const loadingSpinner = page.locator('.animate-spin');
    const loadingText = page.locator('text=Loading documents');
    const signInForm = page.locator('form');

    // Either loading elements should appear or we should see the sign-in form
    // (loading might be too fast to catch consistently)
    await expect(loadingSpinner.or(loadingText).or(signInForm)).toBeVisible({ timeout: 5000 });
  });

  test('should handle document creation workflow after authentication', async ({ page }) => {
    // First sign in anonymously
    await expect(page.locator('form')).toBeVisible({ timeout: 10000 });
    await page.locator('button:has-text("Sign in anonymously")').click();

    // Wait for authentication to complete
    await expect(page.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });

    // For anonymous users, check for the specific restriction message
    await expect(page.locator('text=Limited Access')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=Anonymous users can only view shared documents')).toBeVisible();

    // Anonymous users should NOT see the New Document button
    await expect(page.locator('button:has-text("New Document")')).not.toBeVisible();
  });

  test('should show appropriate empty states', async ({ page }) => {
    // Sign in anonymously first
    await expect(page.locator('form')).toBeVisible({ timeout: 10000 });
    await page.locator('button:has-text("Sign in anonymously")').click();

    // Wait for authentication
    await expect(page.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });

    // Check for empty state messages
    const emptyStateMessages = [
      'No documents yet',
      'No Documents Available',
      'Welcome to Collaborative Editor'
    ];

    let foundEmptyState = false;
    for (const message of emptyStateMessages) {
      const element = page.locator(`text=${message}`);
      if (await element.count() > 0) {
        foundEmptyState = true;
        break;
      }
    }

    expect(foundEmptyState).toBeTruthy();
  });

  test('should be responsive on different screen sizes', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('body')).toBeVisible();
    await expect(page.locator('h2:has-text("Collaborative Editor")')).toBeVisible();

    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('body')).toBeVisible();
    await expect(page.locator('h2:has-text("Collaborative Editor")')).toBeVisible();

    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.locator('body')).toBeVisible();
    await expect(page.locator('h2:has-text("Collaborative Editor")')).toBeVisible();
  });

  test('should handle page reload correctly', async ({ page }) => {
    // Test basic navigation
    const currentUrl = page.url();
    expect(currentUrl).toContain('localhost');

    // Check if the page responds to reload
    await page.reload();
    await expect(page.locator('body')).toBeVisible();
    await expect(page.locator('h2:has-text("Collaborative Editor")')).toBeVisible();
  });

  test('should handle authentication state transitions', async ({ page }) => {
    // Initially should show sign-in form
    await expect(page.locator('form')).toBeVisible({ timeout: 10000 });

    // Sign in anonymously
    await page.locator('button:has-text("Sign in anonymously")').click();

    // Should show authenticated UI
    await expect(page.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });

    // Sign out
    await page.locator('button:has-text("Sign out")').click();

    // Should return to sign-in form
    await expect(page.locator('form')).toBeVisible({ timeout: 10000 });
  });
});

/**
 * Integration tests for the commenting system
 * Tests end-to-end commenting workflows using Playwright
 */

import { test, expect } from '@playwright/test';

test.describe('Commenting System Integration', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the application to load
    await page.waitForSelector('[data-testid="app-loaded"]', { timeout: 10000 });
  });

  test('should create a comment via selection toolbar button', async ({ page }) => {
    // Create a new document first
    await page.click('button:has-text("New Document")');
    await page.waitForSelector('.prose', { timeout: 5000 });

    // Add some text to the editor
    const editor = page.locator('.prose');
    await editor.click();
    await page.keyboard.type('This is a test document with some content to comment on.');

    // Select text for commenting
    await page.keyboard.press('Control+a'); // Select all text

    // Wait for the formatting toolbar to appear with the "Add Comment" button
    await page.waitForSelector('button:has-text("Add Comment")', { timeout: 3000 });

    // Verify the button is in the formatting toolbar (floating selection toolbar)
    const addCommentButton = page.locator('button:has-text("Add Comment")');
    await expect(addCommentButton).toBeVisible();

    // Click "Add Comment" button in the selection toolbar
    await addCommentButton.click();

    // Wait for comment popover to appear
    await page.waitForSelector('[data-testid="comment-popover"]', { timeout: 3000 });

    // Type comment content
    const commentInput = page.locator('textarea[placeholder*="comment"]');
    await commentInput.fill('This is a test comment on the selected text.');

    // Submit the comment
    await page.click('button:has-text("Add Comment")');

    // Verify comment was created
    await page.waitForSelector('.comment-highlight', { timeout: 3000 });
    expect(await page.locator('.comment-highlight').count()).toBe(1);
  });

  test('should show Add Comment button only when text is selected', async ({ page }) => {
    // Create a new document first
    await page.click('button:has-text("New Document")');
    await page.waitForSelector('.prose', { timeout: 5000 });

    // Add some text to the editor
    const editor = page.locator('.prose');
    await editor.click();
    await page.keyboard.type('This is a test document with some content.');

    // Initially, no text is selected, so formatting toolbar should not be visible
    const addCommentButton = page.locator('button:has-text("Add Comment")');
    await expect(addCommentButton).not.toBeVisible();

    // Select text
    await page.keyboard.press('Control+a');

    // Now the formatting toolbar should appear with the Add Comment button
    await expect(addCommentButton).toBeVisible();

    // Click somewhere to deselect
    await editor.click();

    // Formatting toolbar and button should be hidden again
    await expect(addCommentButton).not.toBeVisible();
  });

  test('should open comment sidebar and display comments', async ({ page }) => {
    // Assuming we have a document with comments from previous test
    // or we create one here
    
    // Click the comment toggle button in toolbar
    await page.click('button[title="Toggle Comments"]');
    
    // Verify sidebar opens
    await page.waitForSelector('[data-testid="comment-sidebar"]', { timeout: 3000 });
    
    // Check that sidebar is visible
    const sidebar = page.locator('[data-testid="comment-sidebar"]');
    await expect(sidebar).toBeVisible();
    
    // Verify sidebar shows comment count
    await expect(page.locator('text=Comments')).toBeVisible();
  });

  test('should reply to a comment', async ({ page }) => {
    // Navigate to a document with existing comments
    // This would need to be set up in test data
    
    // Open comment sidebar
    await page.click('button[title="Toggle Comments"]');
    await page.waitForSelector('[data-testid="comment-sidebar"]');
    
    // Click on a comment to expand it
    const firstComment = page.locator('[data-testid="comment-thread"]').first();
    await firstComment.click();
    
    // Click reply button
    await page.click('button:has-text("Reply")');
    
    // Type reply content
    const replyInput = page.locator('textarea[placeholder*="reply"]');
    await replyInput.fill('This is a reply to the comment.');
    
    // Submit reply
    await page.click('button:has-text("Reply")');
    
    // Verify reply appears
    await page.waitForSelector('[data-testid="comment-reply"]', { timeout: 3000 });
    expect(await page.locator('[data-testid="comment-reply"]').count()).toBeGreaterThan(0);
  });

  test('should edit own comment', async ({ page }) => {
    // Open comment sidebar
    await page.click('button[title="Toggle Comments"]');
    await page.waitForSelector('[data-testid="comment-sidebar"]');
    
    // Click on comment options menu
    const commentOptions = page.locator('[data-testid="comment-options"]').first();
    await commentOptions.click();
    
    // Click edit option
    await page.click('text=Edit');
    
    // Modify comment content
    const editInput = page.locator('textarea[value*="test comment"]');
    await editInput.clear();
    await editInput.fill('This is an edited comment.');
    
    // Submit edit
    await page.click('button:has-text("Update")');
    
    // Verify comment was updated
    await expect(page.locator('text=This is an edited comment.')).toBeVisible();
  });

  test('should delete own comment', async ({ page }) => {
    // Open comment sidebar
    await page.click('button[title="Toggle Comments"]');
    await page.waitForSelector('[data-testid="comment-sidebar"]');
    
    // Get initial comment count
    const initialCount = await page.locator('[data-testid="comment-thread"]').count();
    
    // Click on comment options menu
    const commentOptions = page.locator('[data-testid="comment-options"]').first();
    await commentOptions.click();
    
    // Click delete option
    await page.click('text=Delete');
    
    // Confirm deletion in dialog
    await page.click('button:has-text("OK")'); // or whatever the confirm button text is
    
    // Verify comment was deleted
    const finalCount = await page.locator('[data-testid="comment-thread"]').count();
    expect(finalCount).toBe(initialCount - 1);
  });

  test('should resolve and unresolve comments', async ({ page }) => {
    // Open comment sidebar
    await page.click('button[title="Toggle Comments"]');
    await page.waitForSelector('[data-testid="comment-sidebar"]');
    
    // Click on comment options menu
    const commentOptions = page.locator('[data-testid="comment-options"]').first();
    await commentOptions.click();
    
    // Click resolve option
    await page.click('text=Resolve');
    
    // Verify comment is marked as resolved
    await expect(page.locator('text=Resolved')).toBeVisible();
    
    // Click options again to unresolve
    await commentOptions.click();
    await page.click('text=Reopen');
    
    // Verify comment is no longer resolved
    await expect(page.locator('text=Resolved')).not.toBeVisible();
  });

  test('should filter comments by status', async ({ page }) => {
    // Open comment sidebar
    await page.click('button[title="Toggle Comments"]');
    await page.waitForSelector('[data-testid="comment-sidebar"]');
    
    // Click filter dropdown
    await page.click('button:has-text("All")');
    
    // Select "Active" filter
    await page.click('text=Active');
    
    // Verify only active comments are shown
    const activeComments = page.locator('[data-testid="comment-thread"]:not(:has(text="Resolved"))');
    const resolvedComments = page.locator('[data-testid="comment-thread"]:has(text="Resolved")');
    
    expect(await activeComments.count()).toBeGreaterThan(0);
    expect(await resolvedComments.count()).toBe(0);
    
    // Switch to "Resolved" filter
    await page.click('button:has-text("Active")');
    await page.click('text=Resolved');
    
    // Verify only resolved comments are shown
    const newActiveComments = page.locator('[data-testid="comment-thread"]:not(:has(text="Resolved"))');
    const newResolvedComments = page.locator('[data-testid="comment-thread"]:has(text="Resolved")');
    
    expect(await newActiveComments.count()).toBe(0);
    expect(await newResolvedComments.count()).toBeGreaterThan(0);
  });

  test('should highlight commented text when clicking comment', async ({ page }) => {
    // Open comment sidebar
    await page.click('button[title="Toggle Comments"]');
    await page.waitForSelector('[data-testid="comment-sidebar"]');
    
    // Click on a comment in the sidebar
    const firstComment = page.locator('[data-testid="comment-thread"]').first();
    await firstComment.click();
    
    // Verify the corresponding text is highlighted in the editor
    await expect(page.locator('.comment-highlight')).toHaveClass(/ring-2/);
  });

  test('should handle real-time comment updates', async ({ page, context }) => {
    // Open two browser contexts to simulate multiple users
    const page2 = await context.newPage();
    await page2.goto('/');
    await page2.waitForSelector('[data-testid="app-loaded"]', { timeout: 10000 });
    
    // Navigate both to the same document
    // This would require setting up shared document access
    
    // User 1 creates a comment
    await page.click('button[title="Toggle Comments"]');
    await page.click('button:has-text("New")');
    const commentInput = page.locator('textarea[placeholder*="comment"]');
    await commentInput.fill('Real-time test comment');
    await page.click('button:has-text("Add Comment")');
    
    // User 2 should see the comment appear
    await page2.click('button[title="Toggle Comments"]');
    await page2.waitForSelector('[data-testid="comment-sidebar"]');
    
    // Verify comment appears in real-time
    await expect(page2.locator('text=Real-time test comment')).toBeVisible({ timeout: 5000 });
  });

  test('should maintain comment positions during document edits', async ({ page }) => {
    // Create a document with text and comments
    const editor = page.locator('.prose');
    await editor.click();
    await page.keyboard.type('First paragraph.\n\nSecond paragraph with important content.\n\nThird paragraph.');
    
    // Select and comment on second paragraph
    await page.keyboard.press('Control+a');
    await page.keyboard.press('ArrowUp'); // Move to second paragraph
    await page.keyboard.press('Shift+End'); // Select line
    
    await editor.click({ button: 'right' });
    await page.click('text=Add Comment');
    
    const commentInput = page.locator('textarea[placeholder*="comment"]');
    await commentInput.fill('Comment on second paragraph');
    await page.click('button:has-text("Add Comment")');
    
    // Edit text before the commented section
    await page.keyboard.press('Home');
    await page.keyboard.type('NEW CONTENT: ');
    
    // Verify comment is still anchored to correct text
    await page.click('button[title="Toggle Comments"]');
    const comment = page.locator('[data-testid="comment-thread"]').first();
    await comment.click();
    
    // The highlighted text should still be the second paragraph
    await expect(page.locator('.comment-highlight')).toContainText('Second paragraph');
  });
});

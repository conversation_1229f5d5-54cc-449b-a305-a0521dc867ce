/**
 * Integration Tests for ScreenplayEditor Component
 * Tests the screenplay editor functionality end-to-end
 */

import { test, expect } from '@playwright/test';

test.describe('ScreenplayEditor Integration', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    
    // Sign in anonymously to access editor features
    await page.click('button:has-text("Sign in anonymously")');
    await expect(page.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });
  });

  test('should create and access a screenplay document', async ({ page }) => {
    // Create a new script first
    await page.click('button:has-text("New Script")');
    
    // Fill in script details
    await page.fill('input[placeholder="Enter script title..."]', 'Test Screenplay Script');
    await page.click('button:has-text("Create Script")');
    
    // Wait for script to be created and selected
    await expect(page.locator('text=Test Screenplay Script')).toBeVisible({ timeout: 10000 });
    
    // Add a screenplay document to the script
    await page.click('button:has-text("Add Document")');
    
    // Fill in document details
    await page.fill('input[placeholder="Enter document title..."]', 'Test Screenplay');
    
    // Select screenplay document type
    await page.click('[data-testid="document-type-select"]');
    await page.click('text=Screenplay - Industry format');
    
    // Create the document
    await page.click('button:has-text("Create Document")');
    
    // Should navigate to the screenplay document
    await page.waitForURL(/\/script\/.*\/document\/.*/, { timeout: 10000 });
    
    // Check if ScreenplayEditor is rendered
    await expect(page.locator('[data-testid="screenplay-editor"]')).toBeVisible({ timeout: 10000 });
    
    // Check if screenplay toolbar is present
    await expect(page.locator('[data-testid="screenplay-toolbar"]')).toBeVisible({ timeout: 5000 });
    
    // Check if BlockNote editor is present
    await expect(page.locator('[data-testid="blocknote-view"]')).toBeVisible({ timeout: 5000 });
    
    // Check if document header is present
    await expect(page.locator('[data-testid="document-header"]')).toBeVisible({ timeout: 5000 });
  });

  test('should handle screenplay formatting buttons', async ({ page }) => {
    // Create script and screenplay document (reuse from previous test)
    await page.click('button:has-text("New Script")');
    await page.fill('input[placeholder="Enter script title..."]', 'Formatting Test Script');
    await page.click('button:has-text("Create Script")');
    await expect(page.locator('text=Formatting Test Script')).toBeVisible({ timeout: 10000 });
    
    await page.click('button:has-text("Add Document")');
    await page.fill('input[placeholder="Enter document title..."]', 'Formatting Test');
    await page.click('[data-testid="document-type-select"]');
    await page.click('text=Screenplay - Industry format');
    await page.click('button:has-text("Create Document")');
    
    await page.waitForURL(/\/script\/.*\/document\/.*/, { timeout: 10000 });
    await expect(page.locator('[data-testid="screenplay-editor"]')).toBeVisible({ timeout: 10000 });
    
    // Test screenplay formatting buttons
    const formatButtons = [
      'Scene Heading',
      'Action', 
      'Character',
      'Dialogue'
    ];
    
    for (const buttonText of formatButtons) {
      const button = page.locator(`button:has-text("${buttonText}")`);
      await expect(button).toBeVisible({ timeout: 5000 });
      
      // Click the button to test it doesn't cause errors
      await button.click();
      
      // Wait a moment for any potential errors
      await page.waitForTimeout(500);
    }
  });

  test('should inject screenplay CSS styles', async ({ page }) => {
    // Create script and screenplay document
    await page.click('button:has-text("New Script")');
    await page.fill('input[placeholder="Enter script title..."]', 'CSS Test Script');
    await page.click('button:has-text("Create Script")');
    await expect(page.locator('text=CSS Test Script')).toBeVisible({ timeout: 10000 });
    
    await page.click('button:has-text("Add Document")');
    await page.fill('input[placeholder="Enter document title..."]', 'CSS Test');
    await page.click('[data-testid="document-type-select"]');
    await page.click('text=Screenplay - Industry format');
    await page.click('button:has-text("Create Document")');
    
    await page.waitForURL(/\/script\/.*\/document\/.*/, { timeout: 10000 });
    await expect(page.locator('[data-testid="screenplay-editor"]')).toBeVisible({ timeout: 10000 });
    
    // Check if screenplay CSS is injected
    const screenplayStyles = await page.locator('#screenplay-styles');
    await expect(screenplayStyles).toBeAttached();
    
    // Check if the styles contain expected screenplay formatting
    const stylesContent = await screenplayStyles.textContent();
    expect(stylesContent).toContain('screenplay-scene-heading');
    expect(stylesContent).toContain('screenplay-character');
    expect(stylesContent).toContain('screenplay-dialogue');
    expect(stylesContent).toContain('Courier New');
  });

  test('should handle document type validation', async ({ page }) => {
    // Create a research document first
    await page.click('button:has-text("New Script")');
    await page.fill('input[placeholder="Enter script title..."]', 'Type Test Script');
    await page.click('button:has-text("Create Script")');
    await expect(page.locator('text=Type Test Script')).toBeVisible({ timeout: 10000 });
    
    await page.click('button:has-text("Add Document")');
    await page.fill('input[placeholder="Enter document title..."]', 'Research Doc');
    // Don't change document type - should default to research
    await page.click('button:has-text("Create Document")');
    
    await page.waitForURL(/\/script\/.*\/document\/.*/, { timeout: 10000 });
    
    // Should NOT show ScreenplayEditor for research document
    await expect(page.locator('[data-testid="screenplay-editor"]')).not.toBeVisible();
    
    // Should show regular CollaborativeEditor instead
    await expect(page.locator('[data-testid="collaborative-editor"]')).toBeVisible({ timeout: 5000 });
  });

  test('should handle loading states gracefully', async ({ page }) => {
    // Navigate directly to a screenplay URL to test loading states
    await page.goto('/script/invalid-script-id/document/invalid-document-id');
    
    // Should show some kind of loading or error state, not crash
    await page.waitForTimeout(3000);
    
    // Check that the page doesn't have JavaScript errors
    const errors = [];
    page.on('pageerror', error => errors.push(error));
    
    await page.waitForTimeout(2000);
    
    // Should not have any unhandled JavaScript errors
    expect(errors.length).toBe(0);
  });
});

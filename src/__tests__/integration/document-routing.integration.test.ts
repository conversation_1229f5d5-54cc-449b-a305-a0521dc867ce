/**
 * Integration tests for URL-based document routing
 * Tests navigation, URL synchronization, and browser history
 */

import { test, expect } from '@playwright/test';

test.describe('Document URL Routing', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('/');

    // Wait for page to load
    await page.waitForLoadState('networkidle');

    // Sign in using anonymous auth
    await page.locator('button:has-text("Sign in anonymously")').click();

    // Wait for authentication to complete
    await expect(page.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });

    // Wait for the document list to be visible
    await page.waitForSelector('text=My Documents', { timeout: 10000 });
  });

  test('should navigate to document via URL', async ({ page }) => {
    // Create a document first
    await page.click('button:has-text("New Document")');
    await page.fill('input[placeholder*="Document title"]', 'Test Document for URL');
    await page.click('button:has-text("Create")');
    
    // Wait for document to be created and URL to update
    await page.waitForURL(/\/document\/.*/, { timeout: 10000 });
    
    // Verify the URL contains the document ID
    const url = page.url();
    expect(url).toMatch(/\/document\/[a-zA-Z0-9]+$/);
    
    // Verify the document is loaded
    await expect(page.locator('text=Test Document for URL')).toBeVisible();
  });

  test('should handle direct URL navigation to document', async ({ page }) => {
    // Create a document first
    await page.click('button:has-text("New Document")');
    await page.fill('input[placeholder*="Document title"]', 'Direct URL Test Document');
    await page.click('button:has-text("Create")');
    
    // Wait for document to be created
    await page.waitForURL(/\/document\/.*/, { timeout: 10000 });
    const documentUrl = page.url();
    
    // Navigate away
    await page.goto('/');
    await page.waitForSelector('text=My Documents');
    
    // Navigate directly to the document URL
    await page.goto(documentUrl);
    
    // Verify the document loads correctly
    await expect(page.locator('text=Direct URL Test Document')).toBeVisible();
    
    // Verify the document is selected in the sidebar
    await expect(page.locator('.border-blue-500')).toBeVisible();
  });

  test('should handle browser back/forward navigation', async ({ page }) => {
    // Create two documents
    await page.click('button:has-text("New Document")');
    await page.fill('input[placeholder*="Document title"]', 'First Document');
    await page.click('button:has-text("Create")');
    await page.waitForURL(/\/document\/.*/, { timeout: 10000 });
    const firstDocumentUrl = page.url();
    
    await page.click('button:has-text("New Document")');
    await page.fill('input[placeholder*="Document title"]', 'Second Document');
    await page.click('button:has-text("Create")');
    await page.waitForURL(/\/document\/.*/, { timeout: 10000 });
    
    // Verify we're on the second document
    await expect(page.locator('text=Second Document')).toBeVisible();
    
    // Go back to first document
    await page.goBack();
    await page.waitForURL(firstDocumentUrl, { timeout: 5000 });
    await expect(page.locator('text=First Document')).toBeVisible();
    
    // Go forward to second document
    await page.goForward();
    await page.waitForURL(/\/document\/.*/, { timeout: 5000 });
    await expect(page.locator('text=Second Document')).toBeVisible();
  });

  test('should handle invalid document URLs gracefully', async ({ page }) => {
    // Navigate to an invalid document URL
    await page.goto('/document/invalid-document-id');
    
    // Should redirect to home or show appropriate error
    // The exact behavior depends on implementation
    await page.waitForTimeout(2000);
    
    // Should not crash and should show some meaningful content
    const hasErrorMessage = await page.locator('text=Document not found').isVisible();
    const isAtHome = page.url().endsWith('/');
    
    expect(hasErrorMessage || isAtHome).toBeTruthy();
  });

  test('should update URL when selecting documents from sidebar', async ({ page }) => {
    // Create a document
    await page.click('button:has-text("New Document")');
    await page.fill('input[placeholder*="Document title"]', 'Sidebar Selection Test');
    await page.click('button:has-text("Create")');
    await page.waitForURL(/\/document\/.*/, { timeout: 10000 });
    
    // Navigate to home
    await page.goto('/');
    await page.waitForSelector('text=My Documents');
    
    // Click on the document in the sidebar
    await page.click('text=Sidebar Selection Test');
    
    // Verify URL updates
    await page.waitForURL(/\/document\/.*/, { timeout: 5000 });
    
    // Verify document is loaded
    await expect(page.locator('text=Sidebar Selection Test')).toBeVisible();
  });

  test('should handle page refresh on document URL', async ({ page }) => {
    // Create a document
    await page.click('button:has-text("New Document")');
    await page.fill('input[placeholder*="Document title"]', 'Refresh Test Document');
    await page.click('button:has-text("Create")');
    await page.waitForURL(/\/document\/.*/, { timeout: 10000 });
    
    // Refresh the page
    await page.reload();
    
    // Verify the document loads after refresh
    await page.waitForSelector('text=My Documents', { timeout: 10000 });
    await expect(page.locator('text=Refresh Test Document')).toBeVisible();
    
    // Verify the document is still selected in sidebar
    await expect(page.locator('.border-blue-500')).toBeVisible();
  });

  test('should generate shareable URLs', async ({ page }) => {
    // Create a document
    await page.click('button:has-text("New Document")');
    await page.fill('input[placeholder*="Document title"]', 'Shareable Document');
    await page.click('button:has-text("Create")');
    await page.waitForURL(/\/document\/.*/, { timeout: 10000 });
    
    const documentUrl = page.url();
    
    // Open a new tab and navigate to the same URL
    const newPage = await page.context().newPage();
    await newPage.goto(documentUrl);
    
    // Sign in on the new page
    await newPage.locator('button:has-text("Sign in anonymously")').click();
    await expect(newPage.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });
    
    // Verify the document loads in the new tab
    await newPage.waitForSelector('text=My Documents', { timeout: 10000 });
    await expect(newPage.locator('text=Shareable Document')).toBeVisible();
    
    await newPage.close();
  });

  test('should maintain URL consistency across navigation', async ({ page }) => {
    // Create multiple documents
    const documentTitles = ['Doc A', 'Doc B', 'Doc C'];
    const documentUrls: string[] = [];
    
    for (const title of documentTitles) {
      await page.click('button:has-text("New Document")');
      await page.fill('input[placeholder*="Document title"]', title);
      await page.click('button:has-text("Create")');
      await page.waitForURL(/\/document\/.*/, { timeout: 10000 });
      documentUrls.push(page.url());
    }
    
    // Navigate between documents and verify URLs
    for (let i = 0; i < documentUrls.length; i++) {
      await page.goto(documentUrls[i]);
      await expect(page.locator(`text=${documentTitles[i]}`)).toBeVisible();
      expect(page.url()).toBe(documentUrls[i]);
    }
  });
});

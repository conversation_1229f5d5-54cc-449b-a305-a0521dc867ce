/**
 * Integration tests for comment highlighting and selection synchronization
 */

import { test, expect } from '@playwright/test';

test.describe('Comment Highlighting and Selection Synchronization', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    
    // Wait for the app to load
    await page.waitForSelector('body');
    
    // Sign in as a test user
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Wait for authentication
    await page.waitForSelector('button:has-text("New Document")', { timeout: 10000 });
  });

  test('should highlight comments with user-specific colors', async ({ page }) => {
    // Create a new document
    await page.click('button:has-text("New Document")');
    await page.waitForSelector('.prose', { timeout: 5000 });

    // Add some text to the editor
    await page.click('.prose');
    await page.type('.prose', 'This is a test document with some text for commenting.');

    // Select text for commenting
    await page.evaluate(() => {
      const prose = document.querySelector('.prose');
      if (prose) {
        const range = document.createRange();
        const textNode = prose.firstChild;
        if (textNode) {
          range.setStart(textNode, 0);
          range.setEnd(textNode, 10); // Select "This is a "
          const selection = window.getSelection();
          selection?.removeAllRanges();
          selection?.addRange(range);
        }
      }
    });

    // Add comment via toolbar
    await page.click('button[title="Add Comment"]');
    await page.waitForSelector('[data-testid="comment-popover"]');
    
    // Fill in comment
    await page.fill('textarea', 'This is a test comment');
    await page.click('button:has-text("Add Comment")');
    
    // Wait for comment to be created
    await page.waitForTimeout(1000);

    // Check that the text is highlighted with user-specific styling
    const highlightedText = page.locator('.comment-highlight');
    await expect(highlightedText).toBeVisible();
    
    // Verify the highlight has user-specific data attributes
    await expect(highlightedText).toHaveAttribute('data-comment-id');
    await expect(highlightedText).toHaveAttribute('data-user-id');
    
    // Verify the highlight has inline styles for user colors
    const style = await highlightedText.getAttribute('style');
    expect(style).toContain('background-color');
    expect(style).toContain('border');
  });

  test('should synchronize selection between editor and sidebar', async ({ page }) => {
    // Create a document with a comment (reuse setup from previous test)
    await page.click('button:has-text("New Document")');
    await page.waitForSelector('.prose', { timeout: 5000 });
    await page.click('.prose');
    await page.type('.prose', 'Test text for synchronization.');

    // Select and comment on text
    await page.evaluate(() => {
      const prose = document.querySelector('.prose');
      if (prose) {
        const range = document.createRange();
        const textNode = prose.firstChild;
        if (textNode) {
          range.setStart(textNode, 0);
          range.setEnd(textNode, 4); // Select "Test"
          const selection = window.getSelection();
          selection?.removeAllRanges();
          selection?.addRange(range);
        }
      }
    });

    await page.click('button[title="Add Comment"]');
    await page.waitForSelector('[data-testid="comment-popover"]');
    await page.fill('textarea', 'Test comment for sync');
    await page.click('button:has-text("Add Comment")');
    await page.waitForTimeout(1000);

    // Open comment sidebar
    await page.click('button[title="Toggle Comments"]');
    await page.waitForSelector('[data-testid="comment-sidebar"]');

    // Click on the comment in the sidebar
    const commentThread = page.locator('[data-testid="comment-thread"]').first();
    await commentThread.click();

    // Verify the comment is selected (has ring styling)
    await expect(commentThread).toHaveClass(/ring-2/);

    // Verify the corresponding text in the editor is highlighted as selected
    const highlightedText = page.locator('.comment-highlight.comment-selected');
    await expect(highlightedText).toBeVisible();
  });

  test('should handle multiple comments on same text', async ({ page }) => {
    // Create document and add text
    await page.click('button:has-text("New Document")');
    await page.waitForSelector('.prose', { timeout: 5000 });
    await page.click('.prose');
    await page.type('.prose', 'Shared text for multiple comments.');

    // Add first comment
    await page.evaluate(() => {
      const prose = document.querySelector('.prose');
      if (prose) {
        const range = document.createRange();
        const textNode = prose.firstChild;
        if (textNode) {
          range.setStart(textNode, 0);
          range.setEnd(textNode, 6); // Select "Shared"
          const selection = window.getSelection();
          selection?.removeAllRanges();
          selection?.addRange(range);
        }
      }
    });

    await page.click('button[title="Add Comment"]');
    await page.waitForSelector('[data-testid="comment-popover"]');
    await page.fill('textarea', 'First comment');
    await page.click('button:has-text("Add Comment")');
    await page.waitForTimeout(1000);

    // Add second comment on the same text
    await page.evaluate(() => {
      const prose = document.querySelector('.prose');
      if (prose) {
        const range = document.createRange();
        const textNode = prose.firstChild;
        if (textNode) {
          range.setStart(textNode, 0);
          range.setEnd(textNode, 6); // Select "Shared" again
          const selection = window.getSelection();
          selection?.removeAllRanges();
          selection?.addRange(range);
        }
      }
    });

    await page.click('button[title="Add Comment"]');
    await page.waitForSelector('[data-testid="comment-popover"]');
    await page.fill('textarea', 'Second comment');
    await page.click('button:has-text("Add Comment")');
    await page.waitForTimeout(1000);

    // Check that the highlight shows multiple comments
    const highlightedText = page.locator('.comment-highlight');
    await expect(highlightedText).toHaveAttribute('data-comment-count', '2');
    
    // Verify multiple comment indicator is visible
    await expect(highlightedText).toHaveClass(/comment-multiple/);
  });

  test('should show different visual states for active and resolved comments', async ({ page }) => {
    // Create document with comment
    await page.click('button:has-text("New Document")');
    await page.waitForSelector('.prose', { timeout: 5000 });
    await page.click('.prose');
    await page.type('.prose', 'Text for resolution test.');

    // Add comment
    await page.evaluate(() => {
      const prose = document.querySelector('.prose');
      if (prose) {
        const range = document.createRange();
        const textNode = prose.firstChild;
        if (textNode) {
          range.setStart(textNode, 0);
          range.setEnd(textNode, 4); // Select "Text"
          const selection = window.getSelection();
          selection?.removeAllRanges();
          selection?.addRange(range);
        }
      }
    });

    await page.click('button[title="Add Comment"]');
    await page.waitForSelector('[data-testid="comment-popover"]');
    await page.fill('textarea', 'Comment to be resolved');
    await page.click('button:has-text("Add Comment")');
    await page.waitForTimeout(1000);

    // Initially should be active
    const highlightedText = page.locator('.comment-highlight');
    await expect(highlightedText).toHaveClass(/comment-active/);

    // Open sidebar and resolve the comment
    await page.click('button[title="Toggle Comments"]');
    await page.waitForSelector('[data-testid="comment-sidebar"]');
    
    // Click on comment options and resolve
    await page.click('[data-testid="comment-options"]');
    await page.click('text=Resolve');
    await page.waitForTimeout(1000);

    // Should now be resolved
    await expect(highlightedText).toHaveClass(/comment-resolved/);
    await expect(highlightedText).toHaveCSS('opacity', '0.7');
  });

  test('should handle bidirectional click synchronization', async ({ page }) => {
    // Setup document with comment
    await page.click('button:has-text("New Document")');
    await page.waitForSelector('.prose', { timeout: 5000 });
    await page.click('.prose');
    await page.type('.prose', 'Clickable text for bidirectional sync.');

    // Add comment
    await page.evaluate(() => {
      const prose = document.querySelector('.prose');
      if (prose) {
        const range = document.createRange();
        const textNode = prose.firstChild;
        if (textNode) {
          range.setStart(textNode, 0);
          range.setEnd(textNode, 9); // Select "Clickable"
          const selection = window.getSelection();
          selection?.removeAllRanges();
          selection?.addRange(range);
        }
      }
    });

    await page.click('button[title="Add Comment"]');
    await page.waitForSelector('[data-testid="comment-popover"]');
    await page.fill('textarea', 'Bidirectional sync test');
    await page.click('button:has-text("Add Comment")');
    await page.waitForTimeout(1000);

    // Open sidebar
    await page.click('button[title="Toggle Comments"]');
    await page.waitForSelector('[data-testid="comment-sidebar"]');

    // Click on highlighted text in editor
    await page.click('.comment-highlight');
    await page.waitForTimeout(500);

    // Verify sidebar comment is selected
    const commentThread = page.locator('[data-testid="comment-thread"]');
    await expect(commentThread).toHaveClass(/ring-2/);

    // Verify the comment is scrolled into view in sidebar
    const isInViewport = await commentThread.evaluate((el) => {
      const rect = el.getBoundingClientRect();
      return rect.top >= 0 && rect.bottom <= window.innerHeight;
    });
    expect(isInViewport).toBe(true);
  });

  test('should restore comment highlights after page refresh', async ({ page }) => {
    // Create document with comment
    await page.click('button:has-text("New Document")');
    await page.waitForSelector('.prose', { timeout: 5000 });
    await page.click('.prose');
    await page.type('.prose', 'Text that should remain highlighted after refresh.');

    // Add comment
    await page.evaluate(() => {
      const prose = document.querySelector('.prose');
      if (prose) {
        const range = document.createRange();
        const textNode = prose.firstChild;
        if (textNode) {
          range.setStart(textNode, 0);
          range.setEnd(textNode, 4); // Select "Text"
          const selection = window.getSelection();
          selection?.removeAllRanges();
          selection?.addRange(range);
        }
      }
    });

    await page.click('button[title="Add Comment"]');
    await page.waitForSelector('[data-testid="comment-popover"]');
    await page.fill('textarea', 'Comment that should persist after refresh');
    await page.click('button:has-text("Add Comment")');
    await page.waitForTimeout(1000);

    // Verify comment highlight exists before refresh
    const highlightBeforeRefresh = page.locator('.comment-highlight');
    await expect(highlightBeforeRefresh).toBeVisible();

    // Get the current URL to refresh the same document
    const currentUrl = page.url();

    // Refresh the page
    await page.reload();
    await page.waitForSelector('.prose', { timeout: 10000 });

    // Wait a bit for comments to load and decorations to be applied
    await page.waitForTimeout(2000);

    // Verify comment highlight is restored after refresh
    const highlightAfterRefresh = page.locator('.comment-highlight');
    await expect(highlightAfterRefresh).toBeVisible();

    // Verify the highlight has the correct attributes
    await expect(highlightAfterRefresh).toHaveAttribute('data-comment-id');
    await expect(highlightAfterRefresh).toHaveAttribute('data-user-id');

    // Verify the highlight has proper styling
    const style = await highlightAfterRefresh.getAttribute('style');
    expect(style).toContain('background-color');
    expect(style).toContain('border');
  });

  test('should highlight editor text when clicking comment in sidebar', async ({ page }) => {
    // Create document with comment
    await page.click('button:has-text("New Document")');
    await page.waitForSelector('.prose', { timeout: 5000 });
    await page.click('.prose');
    await page.type('.prose', 'Text for sidebar click highlighting test.');

    // Add comment
    await page.evaluate(() => {
      const prose = document.querySelector('.prose');
      if (prose) {
        const range = document.createRange();
        const textNode = prose.firstChild;
        if (textNode) {
          range.setStart(textNode, 0);
          range.setEnd(textNode, 4); // Select "Text"
          const selection = window.getSelection();
          selection?.removeAllRanges();
          selection?.addRange(range);
        }
      }
    });

    await page.click('button[title="Add Comment"]');
    await page.waitForSelector('[data-testid="comment-popover"]');
    await page.fill('textarea', 'Click test comment');
    await page.click('button:has-text("Add Comment")');
    await page.waitForTimeout(1000);

    // Open sidebar
    await page.click('button[title="Toggle Comments"]');
    await page.waitForSelector('[data-testid="comment-sidebar"]');

    // Initially, the comment should not be selected
    const highlightedText = page.locator('.comment-highlight');
    await expect(highlightedText).not.toHaveClass(/comment-selected/);

    // Click on the comment in the sidebar
    const commentThread = page.locator('[data-testid="comment-thread"]').first();
    await commentThread.click();
    await page.waitForTimeout(500);

    // Verify the comment thread is visually selected in sidebar
    await expect(commentThread).toHaveClass(/ring-2/);

    // Verify the corresponding text in the editor is highlighted as selected
    await expect(highlightedText).toHaveClass(/comment-selected/);

    // Verify the selected highlight has enhanced styling
    const selectedStyle = await highlightedText.getAttribute('style');
    expect(selectedStyle).toContain('box-shadow');
    expect(selectedStyle).toContain('transform: scale(1.01)');

    // Verify the selected comment has the pulse animation
    await expect(highlightedText).toHaveCSS('animation-name', 'comment-pulse');
  });
});

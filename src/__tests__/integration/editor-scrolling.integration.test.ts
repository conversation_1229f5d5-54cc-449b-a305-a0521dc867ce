/**
 * Integration Tests for Editor Scrolling Functionality
 * Tests that the ProseMirror editor properly handles scrolling for long documents
 */

import { test, expect } from '@playwright/test';

test.describe('Editor Scrolling', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    
    // Sign in anonymously to access editor features
    await page.click('button:has-text("Sign in anonymously")');
    await expect(page.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });
  });

  test('should allow scrolling when document content exceeds viewport height', async ({ page }) => {
    // Create a new document
    await page.click('button:has-text("New Document")');
    await page.waitForSelector('.prose', { timeout: 5000 });

    // Add a large amount of content to trigger scrolling
    const editor = page.locator('.prose');
    await editor.click();
    
    // Add multiple paragraphs to create a long document
    const longContent = Array.from({ length: 50 }, (_, i) => 
      `This is paragraph ${i + 1}. It contains enough text to make the document quite long and test the scrolling functionality. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.`
    ).join('\n\n');
    
    await page.keyboard.type(longContent);

    // Wait for content to be rendered
    await page.waitForTimeout(1000);

    // Check that the editor container has scrollable area
    const scrollArea = page.locator('[data-radix-scroll-area-viewport]');
    await expect(scrollArea).toBeVisible();

    // Verify that the scroll area has proper height constraints
    const scrollAreaBox = await scrollArea.boundingBox();
    expect(scrollAreaBox).toBeTruthy();
    expect(scrollAreaBox!.height).toBeGreaterThan(0);
    expect(scrollAreaBox!.height).toBeLessThan(2000); // Should be constrained, not unlimited

    // Test scrolling functionality
    const initialScrollTop = await scrollArea.evaluate(el => el.scrollTop);
    
    // Scroll down
    await scrollArea.evaluate(el => el.scrollTo(0, 500));
    await page.waitForTimeout(100);
    
    const newScrollTop = await scrollArea.evaluate(el => el.scrollTop);
    expect(newScrollTop).toBeGreaterThan(initialScrollTop);

    // Verify content is still accessible after scrolling
    await expect(page.locator('text=This is paragraph 1')).toBeVisible();
    await expect(page.locator('text=This is paragraph 50')).toBeVisible();
  });

  test('should maintain cursor position and selection during scroll', async ({ page }) => {
    // Create a new document
    await page.click('button:has-text("New Document")');
    await page.waitForSelector('.prose', { timeout: 5000 });

    const editor = page.locator('.prose');
    await editor.click();
    
    // Add content that will require scrolling
    const content = Array.from({ length: 30 }, (_, i) => 
      `Line ${i + 1}: This is a test line with enough content to test scrolling behavior.`
    ).join('\n');
    
    await page.keyboard.type(content);

    // Position cursor in the middle of the document
    await page.keyboard.press('Control+Home'); // Go to start
    for (let i = 0; i < 15; i++) {
      await page.keyboard.press('ArrowDown');
    }

    // Get scroll area and scroll down
    const scrollArea = page.locator('[data-radix-scroll-area-viewport]');
    await scrollArea.evaluate(el => el.scrollTo(0, 300));
    await page.waitForTimeout(100);

    // Verify cursor is still functional
    await page.keyboard.type(' [CURSOR TEST]');
    await expect(page.locator('text=[CURSOR TEST]')).toBeVisible();
  });

  test('should preserve collaborative features during scrolling', async ({ page }) => {
    // Create a new document
    await page.click('button:has-text("New Document")');
    await page.waitForSelector('.prose', { timeout: 5000 });

    const editor = page.locator('.prose');
    await editor.click();
    
    // Add content
    await page.keyboard.type('This is a test document for collaborative features during scrolling.');

    // Select text for commenting
    await page.keyboard.press('Control+a');

    // Wait for formatting toolbar and add comment button
    await page.waitForSelector('button:has-text("Add Comment")', { timeout: 3000 });
    
    // Scroll the editor area
    const scrollArea = page.locator('[data-radix-scroll-area-viewport]');
    await scrollArea.evaluate(el => el.scrollTo(0, 100));
    await page.waitForTimeout(100);

    // Verify comment functionality still works after scrolling
    const addCommentButton = page.locator('button:has-text("Add Comment")');
    await expect(addCommentButton).toBeVisible();
    
    // Click add comment button
    await addCommentButton.click();

    // Verify comment popover appears
    await page.waitForSelector('[data-testid="comment-popover"]', { timeout: 3000 });
    await expect(page.locator('[data-testid="comment-popover"]')).toBeVisible();
  });

  test('should handle keyboard navigation in scrollable editor', async ({ page }) => {
    // Create a new document
    await page.click('button:has-text("New Document")');
    await page.waitForSelector('.prose', { timeout: 5000 });

    const editor = page.locator('.prose');
    await editor.click();
    
    // Add content that requires scrolling
    const content = Array.from({ length: 40 }, (_, i) => 
      `Paragraph ${i + 1}: Testing keyboard navigation in a scrollable editor environment.`
    ).join('\n\n');
    
    await page.keyboard.type(content);

    // Test keyboard navigation
    await page.keyboard.press('Control+Home'); // Go to start
    await page.keyboard.press('Control+End');   // Go to end
    
    // Verify we can still type at the end
    await page.keyboard.type('\n\nFinal paragraph added via keyboard navigation.');
    await expect(page.locator('text=Final paragraph added via keyboard navigation.')).toBeVisible();

    // Test page up/down navigation
    await page.keyboard.press('PageUp');
    await page.keyboard.press('PageDown');
    
    // Verify editor is still functional
    await page.keyboard.type(' [NAVIGATION TEST]');
    await expect(page.locator('text=[NAVIGATION TEST]')).toBeVisible();
  });

  test('should maintain proper focus management in scrollable area', async ({ page }) => {
    // Create a new document
    await page.click('button:has-text("New Document")');
    await page.waitForSelector('.prose', { timeout: 5000 });

    const editor = page.locator('.prose');
    await editor.click();
    
    // Add content
    await page.keyboard.type('Testing focus management in scrollable editor.');

    // Verify editor has focus
    const focusedElement = await page.evaluate(() => document.activeElement?.tagName);
    expect(['DIV', 'TEXTAREA', 'INPUT'].includes(focusedElement || '')).toBeTruthy();

    // Scroll and verify focus is maintained
    const scrollArea = page.locator('[data-radix-scroll-area-viewport]');
    await scrollArea.evaluate(el => el.scrollTo(0, 200));
    await page.waitForTimeout(100);

    // Type more content to verify focus is still active
    await page.keyboard.type(' Additional content after scroll.');
    await expect(page.locator('text=Additional content after scroll.')).toBeVisible();
  });
});

/**
 * Basic tests for script hierarchy functionality
 */

import { describe, it, expect } from 'vitest';
import { 
  extractIdsFromPath, 
  generateScriptUrl, 
  generateScriptDocumentUrl,
  generateDocumentUrl,
  isValidScriptId,
  isValidDocumentId
} from '../lib/routing';

describe('Script Hierarchy Routing', () => {
  describe('ID validation', () => {
    it('should validate script IDs correctly', () => {
      expect(isValidScriptId('abc123')).toBe(true);
      expect(isValidScriptId('script-id-123')).toBe(true);
      expect(isValidScriptId('')).toBe(false);
      expect(isValidScriptId('admin')).toBe(false);
      expect(isValidScriptId('api')).toBe(false);
      expect(isValidScriptId('id/with/slash')).toBe(false);
    });

    it('should validate document IDs correctly', () => {
      expect(isValidDocumentId('doc123')).toBe(true);
      expect(isValidDocumentId('document-id-456')).toBe(true);
      expect(isValidDocumentId('')).toBe(false);
      expect(isValidDocumentId('settings')).toBe(false);
      expect(isValidDocumentId('id?with=query')).toBe(false);
    });
  });

  describe('URL parsing', () => {
    it('should extract script and document IDs from hierarchical URLs', () => {
      const result = extractIdsFromPath('/script/abc123/document/def456');
      expect(result.scriptId).toBe('abc123');
      expect(result.documentId).toBe('def456');
    });

    it('should extract script ID only from script URLs', () => {
      const result = extractIdsFromPath('/script/abc123');
      expect(result.scriptId).toBe('abc123');
      expect(result.documentId).toBe(null);
    });

    it('should extract document ID from legacy URLs', () => {
      const result = extractIdsFromPath('/document/def456');
      expect(result.scriptId).toBe(null);
      expect(result.documentId).toBe('def456');
    });

    it('should return null for invalid URLs', () => {
      const result = extractIdsFromPath('/invalid/path');
      expect(result.scriptId).toBe(null);
      expect(result.documentId).toBe(null);
    });

    it('should handle URLs with invalid IDs', () => {
      const result = extractIdsFromPath('/script/admin/document/settings');
      expect(result.scriptId).toBe(null);
      expect(result.documentId).toBe(null);
    });
  });

  describe('URL generation', () => {
    it('should generate script URLs correctly', () => {
      const url = generateScriptUrl('abc123' as any);
      expect(url).toBe('/script/abc123');
    });

    it('should generate script document URLs correctly', () => {
      const url = generateScriptDocumentUrl('abc123' as any, 'def456' as any);
      expect(url).toBe('/script/abc123/document/def456');
    });

    it('should generate legacy document URLs correctly', () => {
      const url = generateDocumentUrl('def456' as any);
      expect(url).toBe('/document/def456');
    });
  });

  describe('URL patterns', () => {
    it('should handle complex script/document navigation', () => {
      // Test the full flow: script -> document -> back to script
      const scriptUrl = generateScriptUrl('script123' as any);
      const documentUrl = generateScriptDocumentUrl('script123' as any, 'doc456' as any);
      
      expect(scriptUrl).toBe('/script/script123');
      expect(documentUrl).toBe('/script/script123/document/doc456');
      
      // Parse back
      const scriptResult = extractIdsFromPath(scriptUrl);
      const documentResult = extractIdsFromPath(documentUrl);
      
      expect(scriptResult.scriptId).toBe('script123');
      expect(scriptResult.documentId).toBe(null);
      
      expect(documentResult.scriptId).toBe('script123');
      expect(documentResult.documentId).toBe('doc456');
    });

    it('should maintain backward compatibility with legacy URLs', () => {
      const legacyUrl = generateDocumentUrl('legacy123' as any);
      const result = extractIdsFromPath(legacyUrl);
      
      expect(legacyUrl).toBe('/document/legacy123');
      expect(result.scriptId).toBe(null);
      expect(result.documentId).toBe('legacy123');
    });
  });
});

describe('Document Type System', () => {
  it('should support all document types', () => {
    const documentTypes = ['research', 'screenplay', 'collection'];
    
    documentTypes.forEach(type => {
      expect(['research', 'screenplay', 'collection']).toContain(type);
    });
  });

  it('should default to research type for legacy documents', () => {
    // This would be tested in integration tests with actual database
    const defaultType = 'research';
    expect(defaultType).toBe('research');
  });
});

describe('Navigation Flow', () => {
  it('should support hierarchical navigation patterns', () => {
    // Test the expected navigation flow
    const flows = [
      // Home -> Script -> Document
      { from: '/', to: '/script/abc123', description: 'Home to Script' },
      { from: '/script/abc123', to: '/script/abc123/document/def456', description: 'Script to Document' },
      
      // Direct document access
      { from: '/', to: '/script/abc123/document/def456', description: 'Direct Document Access' },
      
      // Legacy document access
      { from: '/', to: '/document/legacy123', description: 'Legacy Document Access' },
    ];

    flows.forEach(flow => {
      // Parse destination URL
      const result = extractIdsFromPath(flow.to);
      
      // Verify the URL structure is valid
      if (flow.to.includes('/script/') && flow.to.includes('/document/')) {
        expect(result.scriptId).toBeTruthy();
        expect(result.documentId).toBeTruthy();
      } else if (flow.to.includes('/script/')) {
        expect(result.scriptId).toBeTruthy();
        expect(result.documentId).toBe(null);
      } else if (flow.to.includes('/document/')) {
        expect(result.scriptId).toBe(null);
        expect(result.documentId).toBeTruthy();
      }
    });
  });
});

// Mock tests for component integration (would be expanded in actual implementation)
describe('Component Integration', () => {
  it('should handle script selection', () => {
    // Mock test - in real implementation would test actual component behavior
    const mockScriptId = 'script123';
    const expectedUrl = generateScriptUrl(mockScriptId as any);
    expect(expectedUrl).toBe('/script/script123');
  });

  it('should handle document selection within script', () => {
    // Mock test - in real implementation would test actual component behavior
    const mockScriptId = 'script123';
    const mockDocumentId = 'doc456';
    const expectedUrl = generateScriptDocumentUrl(mockScriptId as any, mockDocumentId as any);
    expect(expectedUrl).toBe('/script/script123/document/doc456');
  });

  it('should handle legacy document selection', () => {
    // Mock test - in real implementation would test actual component behavior
    const mockDocumentId = 'legacy123';
    const expectedUrl = generateDocumentUrl(mockDocumentId as any);
    expect(expectedUrl).toBe('/document/legacy123');
  });
});

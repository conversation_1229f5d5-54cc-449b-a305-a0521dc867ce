/**
 * Manual test script for comment highlighting synchronization
 * Run this in the browser console after creating a document with comments
 */

// Test functions to verify comment highlighting fixes
window.testCommentHighlighting = {
  
  /**
   * Test 1: Check if comment decorations are properly restored
   */
  testDecorationRestoration: () => {
    console.log('🧪 Testing comment decoration restoration...');
    
    const highlights = document.querySelectorAll('.comment-highlight');
    console.log(`Found ${highlights.length} comment highlights`);
    
    highlights.forEach((highlight, index) => {
      const commentId = highlight.getAttribute('data-comment-id');
      const userId = highlight.getAttribute('data-user-id');
      const style = highlight.getAttribute('style');
      
      console.log(`Highlight ${index + 1}:`, {
        commentId,
        userId,
        hasBackgroundColor: style?.includes('background-color'),
        hasBorder: style?.includes('border'),
        classes: highlight.className
      });
    });
    
    return highlights.length > 0;
  },

  /**
   * Test 2: Test sidebar click to editor highlighting
   */
  testSidebarClickHighlighting: () => {
    console.log('🧪 Testing sidebar click to editor highlighting...');
    
    const commentThreads = document.querySelectorAll('[data-testid="comment-thread"]');
    if (commentThreads.length === 0) {
      console.log('❌ No comment threads found. Make sure sidebar is open.');
      return false;
    }
    
    console.log(`Found ${commentThreads.length} comment threads`);
    
    // Click on the first comment thread
    const firstThread = commentThreads[0];
    const commentId = firstThread.getAttribute('data-comment-id');
    
    console.log(`Clicking on comment thread: ${commentId}`);
    firstThread.click();
    
    // Check if the corresponding highlight is selected
    setTimeout(() => {
      const selectedHighlight = document.querySelector(`.comment-highlight.comment-selected[data-comment-id="${commentId}"]`);
      if (selectedHighlight) {
        console.log('✅ Comment highlight is properly selected');
        console.log('Selected highlight style:', selectedHighlight.getAttribute('style'));
        return true;
      } else {
        console.log('❌ Comment highlight is not selected');
        return false;
      }
    }, 100);
  },

  /**
   * Test 3: Test editor click to sidebar highlighting
   */
  testEditorClickHighlighting: () => {
    console.log('🧪 Testing editor click to sidebar highlighting...');
    
    const highlights = document.querySelectorAll('.comment-highlight');
    if (highlights.length === 0) {
      console.log('❌ No comment highlights found');
      return false;
    }
    
    // Click on the first highlight
    const firstHighlight = highlights[0];
    const commentId = firstHighlight.getAttribute('data-comment-id');
    
    console.log(`Clicking on comment highlight: ${commentId}`);
    firstHighlight.click();
    
    // Check if the corresponding thread is selected
    setTimeout(() => {
      const selectedThread = document.querySelector(`[data-testid="comment-thread"][data-comment-id="${commentId}"].ring-2`);
      if (selectedThread) {
        console.log('✅ Comment thread is properly selected in sidebar');
        return true;
      } else {
        console.log('❌ Comment thread is not selected in sidebar');
        return false;
      }
    }, 100);
  },

  /**
   * Test 4: Force refresh decorations
   */
  testForceRefresh: () => {
    console.log('🧪 Testing force refresh decorations...');
    
    if (window.debugComments && window.debugComments.forceRefresh) {
      console.log('Forcing decoration refresh...');
      window.debugComments.forceRefresh();
      
      setTimeout(() => {
        const highlights = document.querySelectorAll('.comment-highlight');
        console.log(`After refresh: Found ${highlights.length} comment highlights`);
        return highlights.length > 0;
      }, 100);
    } else {
      console.log('❌ Debug functions not available');
      return false;
    }
  },

  /**
   * Test 5: Debug current state
   */
  debugCurrentState: () => {
    console.log('🧪 Debugging current comment state...');
    
    if (window.debugComments && window.debugComments.debugState) {
      const state = window.debugComments.debugState();
      console.log('Current plugin state:', state);
      return state;
    } else {
      console.log('❌ Debug functions not available');
      return null;
    }
  },

  /**
   * Run all tests
   */
  runAllTests: () => {
    console.log('🚀 Running all comment highlighting tests...');
    console.log('=====================================');
    
    const results = {
      decorationRestoration: testCommentHighlighting.testDecorationRestoration(),
      sidebarClick: testCommentHighlighting.testSidebarClickHighlighting(),
      editorClick: testCommentHighlighting.testEditorClickHighlighting(),
      forceRefresh: testCommentHighlighting.testForceRefresh(),
      debugState: testCommentHighlighting.debugCurrentState()
    };
    
    console.log('=====================================');
    console.log('📊 Test Results:', results);
    
    return results;
  }
};

// Instructions for manual testing
console.log(`
🧪 Comment Highlighting Test Suite Loaded!

To test the fixes:

1. Create a document and add some text
2. Select text and add a comment using the toolbar button
3. Open the comment sidebar
4. Run tests in console:

   // Test individual functions:
   testCommentHighlighting.testDecorationRestoration()
   testCommentHighlighting.testSidebarClickHighlighting()
   testCommentHighlighting.testEditorClickHighlighting()
   testCommentHighlighting.testForceRefresh()
   testCommentHighlighting.debugCurrentState()

   // Or run all tests:
   testCommentHighlighting.runAllTests()

5. To test page refresh:
   - Create comments
   - Refresh the page (F5)
   - Run testCommentHighlighting.testDecorationRestoration()
`);

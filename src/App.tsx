import { Authenticated, Unauthenticated, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { SignInForm } from "./SignInForm";
import { SignOutButton } from "./SignOutButton";
import { Toaster } from "sonner";
import { useState } from "react";
import { Id } from "../convex/_generated/dataModel";
import { DocumentList } from "./components/DocumentList";
import { ScriptList } from "./components/ScriptList";
import { ScriptDocumentList } from "./components/ScriptDocumentList";
import { DocumentEditor } from "./components/DocumentEditor";
import { PresenceIndicator } from "./components/PresenceIndicator";
import { FileText, Users, Film } from "lucide-react";
import { useDocumentRouting } from "./hooks/useDocumentRouting";
import { useScriptRouting } from "./hooks/useScriptRouting";

export default function App() {
  return (
    <div className="h-screen flex flex-col bg-gray-50 overflow-hidden" data-testid="app-loaded">
      <header className="sticky top-0 z-10 py-3 bg-white/80 backdrop-blur-sm h-16 flex justify-between items-center border-b shadow-sm px-4">
        <div className="flex items-center gap-2">
          <Film className="text-blue-600" size={24} />
          <h2 className="text-xl font-semibold text-gray-900">Scripty</h2>
        </div>
        <SignOutButton />
      </header>
      <main className="flex-1 flex">
        <Content />
      </main>
      <Toaster />
    </div>
  );
}

function Content() {
  const loggedInUser = useQuery(api.auth.loggedInUser);

  // Use both routing systems for backward compatibility
  const { selectedDocumentId: legacyDocumentId, navigateToDocument, navigateToHome, isLoading: legacyRoutingLoading } = useDocumentRouting();
  const {
    selectedScriptId,
    selectedDocumentId: scriptDocumentId,
    navigateToScript,
    navigateToScriptDocument,
    isLoading: scriptRoutingLoading
  } = useScriptRouting();

  const [documentCount, setDocumentCount] = useState<number>(0);
  const [scriptCount, setScriptCount] = useState<number>(0);

  // Determine which document is selected (script document takes precedence)
  const selectedDocumentId = scriptDocumentId || legacyDocumentId;
  const isLoading = legacyRoutingLoading || scriptRoutingLoading;

  if (loggedInUser === undefined || isLoading) {
    return (
      <div className="flex-1 flex justify-center items-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <>
      <Unauthenticated>
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="w-full max-w-md mx-auto text-center">
            <div className="mb-8">
              <FileText size={64} className="mx-auto mb-4 text-blue-600" />
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Scripty
              </h1>
              <p className="text-gray-600">
                Create and edit scripts and screenplays together in real-time
              </p>
            </div>
            <SignInForm />
          </div>
        </div>
      </Unauthenticated>

      <Authenticated>
        <div className="flex-1 flex h-full overflow-hidden">
          {/* Left Sidebar - Scripts or Legacy Documents */}
          {selectedScriptId ? (
            // Show script view with documents
            <div className="flex">
              <ScriptList
                onSelectScript={navigateToScript}
                selectedScriptId={selectedScriptId}
                onScriptCountChange={setScriptCount}
              />
              <ScriptDocumentList
                scriptId={selectedScriptId}
                onSelectDocument={navigateToScriptDocument}
                selectedDocumentId={selectedDocumentId}
                onDocumentCountChange={setDocumentCount}
              />
            </div>
          ) : (
            // Show legacy document list and script list
            <div className="flex">
              <ScriptList
                onSelectScript={navigateToScript}
                selectedScriptId={null}
                onScriptCountChange={setScriptCount}
              />
              <DocumentList
                onSelectDocument={navigateToDocument}
                selectedDocumentId={selectedDocumentId}
                onDocumentCountChange={setDocumentCount}
              />
            </div>
          )}

          {/* Main Content Area */}
          <div className="flex-1 flex flex-col bg-gray-50">
            {selectedDocumentId ? (
              <>
                <PresenceIndicator roomId={selectedDocumentId} />
                <DocumentEditor documentId={selectedDocumentId} />
              </>
            ) : selectedScriptId ? (
              // Script selected but no document
              <div className="flex-1 flex items-center justify-center p-8">
                <div className="text-center text-gray-500 max-w-md">
                  <Film size={64} className="mx-auto mb-6 opacity-50" />
                  <h2 className="text-2xl font-semibold mb-3 text-gray-900">Script Selected</h2>
                  <p className="mb-6 text-gray-600">
                    {documentCount === 0
                      ? "Add your first document to this script to get started"
                      : "Select a document from the script to start editing"
                    }
                  </p>
                  <div className="flex items-center justify-center gap-6 text-sm">
                    <div className="flex items-center gap-2">
                      <Users size={18} className="text-blue-600" />
                      <span>Real-time collaboration</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Film size={18} className="text-blue-600" />
                      <span>Screenplay formatting</span>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              // No script or document selected
              <div className="flex-1 flex items-center justify-center p-8">
                <div className="text-center text-gray-500 max-w-md">
                  <FileText size={64} className="mx-auto mb-6 opacity-50" />
                  {scriptCount === 0 && documentCount === 0 ? (
                    <>
                      <h2 className="text-2xl font-semibold mb-3 text-gray-900">Welcome to Scripty</h2>
                      <p className="mb-6 text-gray-600">Create your first script or document to get started</p>
                    </>
                  ) : (
                    <>
                      <h2 className="text-2xl font-semibold mb-3 text-gray-900">Welcome to Scripty</h2>
                      <p className="mb-6 text-gray-600">Select a script from the sidebar to start editing</p>
                    </>
                  )}
                  <div className="flex items-center justify-center gap-6 text-sm">
                    <div className="flex items-center gap-2">
                      <Users size={18} className="text-blue-600" />
                      <span>Real-time collaboration</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <FileText size={18} className="text-blue-600" />
                      <span>Rich text editing</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Film size={18} className="text-blue-600" />
                      <span>Screenplay formatting</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </Authenticated>
    </>
  );
}

// Test script to check ScreenplayEditor functionality
// Run this in the browser console

console.log('=== ScreenplayEditor Test Script ===');

// Test 1: Check if ScreenplayEditor component is available
try {
  const screenplayEditorElements = document.querySelectorAll('[data-testid="screenplay-editor"], .screenplay-editor');
  console.log('✅ ScreenplayEditor elements found:', screenplayEditorElements.length);
  
  if (screenplayEditorElements.length > 0) {
    console.log('✅ ScreenplayEditor is rendered on the page');
  } else {
    console.log('ℹ️ ScreenplayEditor not found (may not be on screenplay page)');
  }
} catch (error) {
  console.error('❌ Error checking ScreenplayEditor:', error);
}

// Test 2: Check if screenplay CSS is injected
try {
  const screenplayStyles = document.getElementById('screenplay-styles');
  if (screenplayStyles) {
    console.log('✅ Screenplay CSS styles are injected');
    console.log('CSS content preview:', screenplayStyles.textContent.substring(0, 200) + '...');
  } else {
    console.log('❌ Screenplay CSS styles not found');
  }
} catch (error) {
  console.error('❌ Error checking screenplay CSS:', error);
}

// Test 3: Check for any JavaScript errors related to screenplay
const originalError = console.error;
let screenplayErrors = [];
console.error = function(...args) {
  if (args[0] && args[0].toString().toLowerCase().includes('screenplay')) {
    screenplayErrors.push(args);
  }
  originalError.apply(console, args);
};

// Test 4: Check if we're on a screenplay document page
try {
  const currentUrl = window.location.href;
  const isScreenplayPage = currentUrl.includes('/script/') && currentUrl.includes('/document/');
  console.log('Current URL:', currentUrl);
  console.log('Is screenplay page:', isScreenplayPage);
  
  if (isScreenplayPage) {
    console.log('✅ On a script document page');
  } else {
    console.log('ℹ️ Not on a script document page');
  }
} catch (error) {
  console.error('❌ Error checking URL:', error);
}

// Test 5: Check for BlockNote editor
try {
  const blockNoteElements = document.querySelectorAll('[data-testid="blocknote-view"], .bn-editor');
  console.log('BlockNote elements found:', blockNoteElements.length);
  
  if (blockNoteElements.length > 0) {
    console.log('✅ BlockNote editor is present');
  } else {
    console.log('❌ BlockNote editor not found');
  }
} catch (error) {
  console.error('❌ Error checking BlockNote:', error);
}

// Test 6: Check for screenplay toolbar
try {
  const toolbarElements = document.querySelectorAll('[data-testid="screenplay-toolbar"], .screenplay-toolbar');
  console.log('Screenplay toolbar elements found:', toolbarElements.length);
  
  if (toolbarElements.length > 0) {
    console.log('✅ Screenplay toolbar is present');
  } else {
    console.log('❌ Screenplay toolbar not found');
  }
} catch (error) {
  console.error('❌ Error checking screenplay toolbar:', error);
}

// Test 7: Check for any React errors
try {
  const reactErrors = window.__REACT_DEVTOOLS_GLOBAL_HOOK__?.onCommitFiberRoot;
  if (reactErrors) {
    console.log('✅ React DevTools available for debugging');
  }
} catch (error) {
  console.log('ℹ️ React DevTools not available');
}

console.log('=== Test Complete ===');
console.log('If you see any ❌ errors above, those indicate issues with the ScreenplayEditor');
console.log('To test ScreenplayEditor:');
console.log('1. Create a new script');
console.log('2. Add a screenplay document to the script');
console.log('3. Navigate to the screenplay document');
console.log('4. Run this script again to check for errors');

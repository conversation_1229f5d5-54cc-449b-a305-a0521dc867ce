// Debug script to identify ScreenplayEditor issues
// Run this in the browser console on the screenplay document page

console.log('=== ScreenplayEditor Debug Script ===');

// Check if we're on a screenplay document page
const currentUrl = window.location.href;
console.log('Current URL:', currentUrl);

// Check for ScreenplayEditor component
const screenplayEditor = document.querySelector('[data-testid="screenplay-editor"]');
console.log('ScreenplayEditor element found:', !!screenplayEditor);

if (screenplayEditor) {
  console.log('ScreenplayEditor HTML:', screenplayEditor.outerHTML.substring(0, 500) + '...');
}

// Check for error messages
const errorMessages = document.querySelectorAll('*').forEach(el => {
  const text = el.textContent;
  if (text && (text.includes('Editor Failed to Load') || text.includes('could not be initialized'))) {
    console.log('Error message found:', text);
    console.log('Error element:', el);
  }
});

// Check for BlockNote editor
const blockNoteEditor = document.querySelector('[data-testid="blocknote-view"], .bn-editor');
console.log('BlockNote editor found:', !!blockNoteEditor);

// Check for screenplay CSS
const screenplayStyles = document.getElementById('screenplay-styles');
console.log('Screenplay CSS injected:', !!screenplayStyles);
if (screenplayStyles) {
  console.log('CSS content preview:', screenplayStyles.textContent.substring(0, 200) + '...');
}

// Check for JavaScript errors
const errors = [];
const originalError = console.error;
console.error = function(...args) {
  errors.push(args);
  originalError.apply(console, args);
};

// Check React component tree
try {
  const reactFiber = screenplayEditor?._reactInternalFiber || screenplayEditor?.__reactInternalInstance;
  console.log('React fiber found:', !!reactFiber);
} catch (e) {
  console.log('React fiber check failed:', e.message);
}

// Check for specific hook errors
const hookErrors = errors.filter(error => 
  error.some(arg => 
    typeof arg === 'string' && 
    (arg.includes('useScreenplayFormatting') || 
     arg.includes('useBlockNoteSync') || 
     arg.includes('BlockNote'))
  )
);

console.log('Hook-related errors:', hookErrors);

// Check if document data is loading
try {
  // This will help us see if the queries are working
  const documentData = window.__CONVEX_CLIENT__?.localQueryResults;
  console.log('Convex query results available:', !!documentData);
} catch (e) {
  console.log('Convex client check failed:', e.message);
}

// Check for component mount issues
setTimeout(() => {
  console.log('=== After 2 seconds ===');
  const updatedScreenplayEditor = document.querySelector('[data-testid="screenplay-editor"]');
  console.log('ScreenplayEditor still present:', !!updatedScreenplayEditor);
  
  const loadingSpinner = document.querySelector('.animate-spin');
  console.log('Loading spinner present:', !!loadingSpinner);
  
  const errorState = document.querySelector('*').textContent?.includes('Editor Failed to Load');
  console.log('Error state showing:', errorState);
  
  console.log('Total errors captured:', errors.length);
  if (errors.length > 0) {
    console.log('Recent errors:', errors.slice(-5));
  }
}, 2000);

console.log('=== Debug script complete ===');
console.log('To test: Navigate to a screenplay document and run this script');

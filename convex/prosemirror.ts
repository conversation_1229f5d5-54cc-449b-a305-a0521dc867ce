import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { components, api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Id } from "./_generated/dataModel";

export const getSnapshot = query({
  args: { 
    id: v.string(),
    version: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check if user has access to this document
    try {
      const documentId = args.id as Id<"documents">;
      const permission = await ctx.runQuery(api.sharing.getDocumentPermission, { documentId });
      
      if (!permission) {
        throw new Error("Access denied");
      }
    } catch (error) {
      // If document doesn't exist or permission check fails, allow prosemirror to handle it
      // This might be a new document being created
    }

    return await ctx.runQuery(components.prosemirrorSync.lib.getSnapshot, args);
  },
});

export const submitSnapshot = mutation({
  args: {
    id: v.string(),
    content: v.string(),
    version: v.number(),
    pruneSnapshots: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check if user has write access to this document
    const documentId = args.id as any; // Convert string to document ID
    const permission = await ctx.runQuery(api.sharing.getDocumentPermission, { documentId });
    
    if (!permission || permission.permission === "read") {
      throw new Error("Write access denied");
    }

    return await ctx.runMutation(components.prosemirrorSync.lib.submitSnapshot, args);
  },
});

export const latestVersion = query({
  args: { id: v.string() },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check if user has access to this document
    try {
      const documentId = args.id as any; // Convert string to document ID
      const permission = await ctx.runQuery(api.sharing.getDocumentPermission, { documentId });

      if (!permission) {
        throw new Error("Access denied");
      }
    } catch (error) {
      // If document doesn't exist or permission check fails, allow prosemirror to handle it
      // This might be a new document being created
      console.log("Permission check failed for latestVersion:", error);
    }

    return await ctx.runQuery(components.prosemirrorSync.lib.latestVersion, args);
  },
});

export const getSteps = query({
  args: {
    id: v.string(),
    version: v.number()
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check if user has access to this document
    try {
      const documentId = args.id as any; // Convert string to document ID
      const permission = await ctx.runQuery(api.sharing.getDocumentPermission, { documentId });

      if (!permission) {
        throw new Error("Access denied");
      }
    } catch (error) {
      // If document doesn't exist or permission check fails, allow prosemirror to handle it
      // This might be a new document being created
      console.log("Permission check failed for getSteps:", error);
    }

    return await ctx.runQuery(components.prosemirrorSync.lib.getSteps, args);
  },
});

export const submitSteps = mutation({
  args: {
    id: v.string(),
    steps: v.array(v.string()),
    version: v.number(),
    clientId: v.union(v.string(), v.number()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check if user has write access to this document
    const documentId = args.id as any; // Convert string to document ID
    const permission = await ctx.runQuery(api.sharing.getDocumentPermission, { documentId });
    
    if (!permission || permission.permission === "read") {
      throw new Error("Write access denied");
    }

    return await ctx.runMutation(components.prosemirrorSync.lib.submitSteps, args);
  },
});

import { mutation, query } from "./_generated/server";
import { components } from "./_generated/api";
import { v } from "convex/values";
import { Presence } from "@convex-dev/presence";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Id } from "./_generated/dataModel";

export const presence = new Presence(components.presence);

// Define the cursor position and selection data structure
const cursorDataValidator = v.object({
  position: v.number(),
  selection: v.optional(v.object({
    from: v.number(),
    to: v.number(),
  })),
  timestamp: v.number(),
});

export const getUserId = query({
  args: {},
  returns: v.union(v.string(), v.null()),
  handler: async (ctx) => {
    return await getAuthUserId(ctx);
  },
});

export const heartbeat = mutation({
  args: { roomId: v.string(), userId: v.string(), sessionId: v.string(), interval: v.number() },
  handler: async (ctx, { roomId, userId, sessionId, interval }) => {
    const authUserId = await getAuthUserId(ctx);
    if (!authUserId) {
      // Return a dummy response to match expected type
      return { roomToken: "", sessionToken: "" };
    }
    return await presence.heartbeat(ctx, roomId, authUserId, sessionId, interval);
  },
});

export const list = query({
  args: { roomToken: v.string() },
  handler: async (ctx, { roomToken }) => {
    const presenceList = await presence.list(ctx, roomToken);
    const listWithUserInfo = await Promise.all(
      presenceList.map(async (entry) => {
        const user = await ctx.db.get(entry.userId as Id<"users">);
        if (!user) {
          return entry;
        }
        return {
          ...entry,
          name: user?.name || user?.email || "Anonymous",
          image: user?.image,
        };
      })
    );
    return listWithUserInfo;
  },
});

export const disconnect = mutation({
  args: { sessionToken: v.string() },
  handler: async (ctx, { sessionToken }) => {
    return await presence.disconnect(ctx, sessionToken);
  },
});

// Update cursor position and selection for collaborative editing
export const updateCursor = mutation({
  args: {
    documentId: v.id("documents"),
    cursorData: cursorDataValidator,
  },
  handler: async (ctx, { documentId, cursorData }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check if user has permission to access this document
    const document = await ctx.db.get(documentId);
    if (!document) {
      throw new Error("Document not found");
    }

    // Check if user is the owner
    if (document.createdBy !== userId) {
      // Check if user has been shared access
      const share = await ctx.db
        .query("documentShares")
        .withIndex("by_document_and_user", (q) =>
          q.eq("documentId", documentId).eq("sharedWithUserId", userId)
        )
        .first();

      if (!share) {
        throw new Error("Access denied");
      }
    }

    // Store cursor data in a separate table for real-time updates
    const existingCursor = await ctx.db
      .query("cursors")
      .withIndex("by_document_user", (q) =>
        q.eq("documentId", documentId).eq("userId", userId)
      )
      .first();

    if (existingCursor) {
      await ctx.db.patch(existingCursor._id, {
        position: cursorData.position,
        selection: cursorData.selection,
        timestamp: cursorData.timestamp,
        lastActive: Date.now(),
      });
    } else {
      await ctx.db.insert("cursors", {
        documentId,
        userId,
        position: cursorData.position,
        selection: cursorData.selection,
        timestamp: cursorData.timestamp,
        lastActive: Date.now(),
      });
    }
  },
});

// Get all cursor positions for a document
export const getCursors = query({
  args: { documentId: v.id("documents") },
  handler: async (ctx, { documentId }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    // Check if user has permission to access this document
    const document = await ctx.db.get(documentId);
    if (!document) {
      return [];
    }

    // Check if user is the owner
    if (document.createdBy !== userId) {
      // Check if user has been shared access
      const share = await ctx.db
        .query("documentShares")
        .withIndex("by_document_and_user", (q) =>
          q.eq("documentId", documentId).eq("sharedWithUserId", userId)
        )
        .first();

      if (!share) {
        return [];
      }
    }

    // Get all active cursors for this document (excluding current user)
    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;

    const cursors = await ctx.db
      .query("cursors")
      .withIndex("by_document", (q) => q.eq("documentId", documentId))
      .filter((q) =>
        q.and(
          q.neq(q.field("userId"), userId), // Exclude current user
          q.gt(q.field("lastActive"), fiveMinutesAgo) // Only active cursors
        )
      )
      .collect();

    // Get user info for each cursor
    const cursorsWithUserInfo = await Promise.all(
      cursors.map(async (cursor) => {
        const user = await ctx.db.get(cursor.userId as Id<"users">);
        if (!user) {
          return null;
        }
        return {
          userId: cursor.userId,
          sessionId: `${cursor.userId}-session`, // Stable session ID per user
          name: user?.name || user?.email || "Anonymous",
          image: user?.image,
          cursor: {
            position: cursor.position,
            selection: cursor.selection,
            timestamp: cursor.timestamp,
          },
        };
      })
    );

    return cursorsWithUserInfo.filter(Boolean);
  },
});

// Clean up stale cursors (called periodically)
export const cleanupStaleCursors = mutation({
  args: { documentId: v.id("documents") },
  handler: async (ctx, { documentId }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return;
    }

    // Clean up cursors older than 5 minutes
    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
    const staleCursors = await ctx.db
      .query("cursors")
      .withIndex("by_document", (q) => q.eq("documentId", documentId))
      .filter((q) => q.lt(q.field("lastActive"), fiveMinutesAgo))
      .collect();

    // Delete stale cursors
    for (const cursor of staleCursors) {
      await ctx.db.delete(cursor._id);
    }

    return { cleaned: staleCursors.length };
  },
});

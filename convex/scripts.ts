import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Id } from "./_generated/dataModel";

export const createScript = mutation({
  args: {
    title: v.optional(v.string()),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("You must be signed in to create scripts");
    }

    const scriptId = await ctx.db.insert("scripts", {
      title: args.title || "Untitled Script",
      description: args.description,
      createdBy: userId,
      isPublic: false,
    });

    return scriptId;
  },
});

export const getScript = query({
  args: { id: v.id("scripts") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const script = await ctx.db.get(args.id);
    if (!script) {
      return null;
    }

    // Check if user is the owner
    if (script.createdBy === userId) {
      return script;
    }

    // Check if script is public
    if (script.isPublic) {
      return script;
    }

    // Check if user has been shared access
    const share = await ctx.db
      .query("scriptShares")
      .withIndex("by_script_and_user", (q) =>
        q.eq("scriptId", args.id).eq("sharedWithUserId", userId)
      )
      .first();

    if (share) {
      return script;
    }

    return null;
  },
});

export const getUserScripts = query({
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    // Get scripts owned by user
    const ownedScripts = await ctx.db
      .query("scripts")
      .withIndex("by_created_by", (q) => q.eq("createdBy", userId))
      .collect();

    // Get scripts shared with user
    const sharedScriptIds = await ctx.db
      .query("scriptShares")
      .withIndex("by_user", (q) => q.eq("sharedWithUserId", userId))
      .collect();

    const sharedScripts = await Promise.all(
      sharedScriptIds.map(async (share) => {
        const script = await ctx.db.get(share.scriptId);
        if (script) {
          return {
            ...script,
            permission: share.permission,
            sharedBy: share.sharedBy,
          };
        }
        return null;
      })
    );

    const validSharedScripts = sharedScripts.filter(Boolean);

    // Combine and add permission info
    const allScripts = [
      ...ownedScripts.map((script) => ({
        ...script,
        permission: "owner" as const,
      })),
      ...validSharedScripts,
    ];

    // Sort by creation time (newest first)
    return allScripts
      .filter((script): script is NonNullable<typeof script> => script !== null)
      .sort((a, b) => b._creationTime - a._creationTime);
  },
});

export const updateScript = mutation({
  args: {
    id: v.id("scripts"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    isPublic: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("You must be signed in to update scripts");
    }

    const script = await ctx.db.get(args.id);
    if (!script) {
      throw new Error("Script not found");
    }

    // Check if user has write permission
    if (script.createdBy !== userId) {
      const share = await ctx.db
        .query("scriptShares")
        .withIndex("by_script_and_user", (q) =>
          q.eq("scriptId", args.id).eq("sharedWithUserId", userId)
        )
        .first();

      if (!share || share.permission !== "write") {
        throw new Error("You don't have permission to edit this script");
      }
    }

    const updates: any = {};
    if (args.title !== undefined) updates.title = args.title;
    if (args.description !== undefined) updates.description = args.description;
    if (args.isPublic !== undefined) updates.isPublic = args.isPublic;

    await ctx.db.patch(args.id, updates);
    return args.id;
  },
});

export const deleteScript = mutation({
  args: { id: v.id("scripts") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("You must be signed in to delete scripts");
    }

    const script = await ctx.db.get(args.id);
    if (!script) {
      throw new Error("Script not found");
    }

    // Only owners can delete scripts
    if (script.createdBy !== userId) {
      throw new Error("Only script owners can delete scripts");
    }

    // Delete all documents in this script
    const documents = await ctx.db
      .query("documents")
      .withIndex("by_script", (q) => q.eq("scriptId", args.id))
      .collect();

    for (const document of documents) {
      // Delete document shares
      const docShares = await ctx.db
        .query("documentShares")
        .withIndex("by_document", (q) => q.eq("documentId", document._id))
        .collect();
      for (const share of docShares) {
        await ctx.db.delete(share._id);
      }

      // Delete document invitations
      const docInvitations = await ctx.db
        .query("documentInvitations")
        .withIndex("by_document", (q) => q.eq("documentId", document._id))
        .collect();
      for (const invitation of docInvitations) {
        await ctx.db.delete(invitation._id);
      }

      // Delete permission requests
      const docRequests = await ctx.db
        .query("permissionRequests")
        .withIndex("by_document", (q) => q.eq("documentId", document._id))
        .collect();
      for (const request of docRequests) {
        await ctx.db.delete(request._id);
      }

      // Delete the document
      await ctx.db.delete(document._id);
    }

    // Delete all script shares
    const scriptShares = await ctx.db
      .query("scriptShares")
      .withIndex("by_script", (q) => q.eq("scriptId", args.id))
      .collect();
    for (const share of scriptShares) {
      await ctx.db.delete(share._id);
    }

    // Delete all script invitations
    const scriptInvitations = await ctx.db
      .query("scriptInvitations")
      .withIndex("by_script", (q) => q.eq("scriptId", args.id))
      .collect();
    for (const invitation of scriptInvitations) {
      await ctx.db.delete(invitation._id);
    }

    // Delete script permission requests
    const scriptRequests = await ctx.db
      .query("scriptPermissionRequests")
      .withIndex("by_script", (q) => q.eq("scriptId", args.id))
      .collect();
    for (const request of scriptRequests) {
      await ctx.db.delete(request._id);
    }

    // Delete the script
    await ctx.db.delete(args.id);
    return args.id;
  },
});

export const canCreateScripts = query({
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    
    if (!userId) {
      return {
        canCreate: false,
        reason: "not_authenticated"
      };
    }

    const user = await ctx.db.get(userId);
    if (!user) {
      return {
        canCreate: false,
        reason: "user_not_found"
      };
    }

    // Check if user is anonymous
    if (!user.email) {
      return {
        canCreate: false,
        reason: "anonymous_user"
      };
    }

    return {
      canCreate: true
    };
  },
});

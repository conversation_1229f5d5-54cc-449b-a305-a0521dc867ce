import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const shareScript = mutation({
  args: {
    scriptId: v.id("scripts"),
    userEmail: v.string(),
    permission: v.union(v.literal("read"), v.literal("write")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check if user owns the script
    const script = await ctx.db.get(args.scriptId);
    if (!script) {
      throw new Error("Script not found");
    }

    if (script.createdBy !== userId) {
      throw new Error("Only script owners can share scripts");
    }

    // Find the user to share with
    const targetUser = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("email"), args.userEmail))
      .first();

    if (!targetUser) {
      throw new Error("User not found");
    }

    if (targetUser._id === userId) {
      throw new Error("Cannot share script with yourself");
    }

    // Check if already shared
    const existingShare = await ctx.db
      .query("scriptShares")
      .withIndex("by_script_and_user", (q) =>
        q.eq("scriptId", args.scriptId).eq("sharedWithUserId", targetUser._id)
      )
      .first();

    if (existingShare) {
      // Update existing share
      await ctx.db.patch(existingShare._id, {
        permission: args.permission,
      });
    } else {
      // Create new share
      await ctx.db.insert("scriptShares", {
        scriptId: args.scriptId,
        sharedWithUserId: targetUser._id,
        permission: args.permission,
        sharedBy: userId,
      });
    }

    return { success: true };
  },
});

export const getScriptShares = query({
  args: { scriptId: v.id("scripts") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    // Check if user owns the script
    const script = await ctx.db.get(args.scriptId);
    if (!script || script.createdBy !== userId) {
      return [];
    }

    const shares = await ctx.db
      .query("scriptShares")
      .withIndex("by_script", (q) => q.eq("scriptId", args.scriptId))
      .collect();

    const sharesWithUsers = await Promise.all(
      shares.map(async (share) => {
        const user = await ctx.db.get(share.sharedWithUserId);
        return {
          ...share,
          user: user ? {
            _id: user._id,
            name: user.name,
            email: user.email,
          } : null,
        };
      })
    );

    return sharesWithUsers.filter((share) => share.user !== null);
  },
});

export const removeScriptShare = mutation({
  args: {
    scriptId: v.id("scripts"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const currentUserId = await getAuthUserId(ctx);
    if (!currentUserId) {
      throw new Error("Not authenticated");
    }

    // Check if user owns the script
    const script = await ctx.db.get(args.scriptId);
    if (!script || script.createdBy !== currentUserId) {
      throw new Error("Only script owners can remove shares");
    }

    const share = await ctx.db
      .query("scriptShares")
      .withIndex("by_script_and_user", (q) =>
        q.eq("scriptId", args.scriptId).eq("sharedWithUserId", args.userId)
      )
      .first();

    if (share) {
      await ctx.db.delete(share._id);
    }

    return { success: true };
  },
});

export const getScriptPermission = query({
  args: { scriptId: v.id("scripts") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const script = await ctx.db.get(args.scriptId);
    if (!script) {
      return null;
    }

    // Get owner information
    const owner = await ctx.db.get(script.createdBy);

    // Owner has full access
    if (script.createdBy === userId) {
      return {
        permission: "owner",
        canShare: true,
        owner: owner ? {
          _id: owner._id,
          name: owner.name,
          email: owner.email
        } : null
      };
    }

    // Check shared access
    const share = await ctx.db
      .query("scriptShares")
      .withIndex("by_script_and_user", (q) =>
        q.eq("scriptId", args.scriptId).eq("sharedWithUserId", userId)
      )
      .first();

    if (share) {
      return {
        permission: share.permission,
        canShare: false,
        owner: owner ? {
          _id: owner._id,
          name: owner.name,
          email: owner.email
        } : null
      };
    }

    // Check if script is public
    if (script.isPublic) {
      return {
        permission: "read",
        canShare: false,
        owner: owner ? {
          _id: owner._id,
          name: owner.name,
          email: owner.email
        } : null
      };
    }

    return null;
  },
});

export const requestScriptPermission = mutation({
  args: {
    scriptId: v.id("scripts"),
    message: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const script = await ctx.db.get(args.scriptId);
    if (!script) {
      throw new Error("Script not found");
    }

    if (script.createdBy === userId) {
      throw new Error("Cannot request permission for your own script");
    }

    // Check if user already has permission
    const existingShare = await ctx.db
      .query("scriptShares")
      .withIndex("by_script_and_user", (q) =>
        q.eq("scriptId", args.scriptId).eq("sharedWithUserId", userId)
      )
      .first();

    if (existingShare) {
      throw new Error("You already have access to this script");
    }

    // Check if there's already a pending request
    const existingRequest = await ctx.db
      .query("scriptPermissionRequests")
      .withIndex("by_script_and_user", (q) =>
        q.eq("scriptId", args.scriptId).eq("requestedBy", userId)
      )
      .filter((q) => q.eq(q.field("status"), "pending"))
      .first();

    if (existingRequest) {
      throw new Error("You already have a pending request for this script");
    }

    // Create permission request
    await ctx.db.insert("scriptPermissionRequests", {
      scriptId: args.scriptId,
      requestedBy: userId,
      currentPermission: "read",
      requestedPermission: "write",
      status: "pending",
      message: args.message,
    });

    return { success: true };
  },
});

export const getScriptPermissionRequests = query({
  args: { scriptId: v.id("scripts") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    // Check if user owns the script
    const script = await ctx.db.get(args.scriptId);
    if (!script || script.createdBy !== userId) {
      return [];
    }

    const requests = await ctx.db
      .query("scriptPermissionRequests")
      .withIndex("by_script", (q) => q.eq("scriptId", args.scriptId))
      .filter((q) => q.eq(q.field("status"), "pending"))
      .collect();

    const requestsWithUsers = await Promise.all(
      requests.map(async (request) => {
        const user = await ctx.db.get(request.requestedBy);
        return {
          ...request,
          user: user ? {
            _id: user._id,
            name: user.name,
            email: user.email,
          } : null,
        };
      })
    );

    return requestsWithUsers.filter((request) => request.user !== null);
  },
});

export const reviewScriptPermissionRequest = mutation({
  args: {
    requestId: v.id("scriptPermissionRequests"),
    action: v.union(v.literal("approve"), v.literal("deny")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const request = await ctx.db.get(args.requestId);
    if (!request) {
      throw new Error("Request not found");
    }

    // Check if user owns the script
    const script = await ctx.db.get(request.scriptId);
    if (!script || script.createdBy !== userId) {
      throw new Error("Access denied");
    }

    // Update request status
    await ctx.db.patch(args.requestId, {
      status: args.action === "approve" ? "approved" : "denied",
      reviewedBy: userId,
      reviewedAt: Date.now(),
    });

    // If approved, create the share
    if (args.action === "approve") {
      await ctx.db.insert("scriptShares", {
        scriptId: request.scriptId,
        sharedWithUserId: request.requestedBy,
        permission: request.requestedPermission,
        sharedBy: userId,
      });
    }

    return { success: true };
  },
});

import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

const applicationTables = {
  scripts: defineTable({
    title: v.string(),
    description: v.optional(v.string()),
    createdBy: v.id("users"),
    isPublic: v.optional(v.boolean()),
  }).index("by_created_by", ["createdBy"]),

  documents: defineTable({
    title: v.string(),
    createdBy: v.id("users"),
    scriptId: v.optional(v.id("scripts")), // Optional for backward compatibility
    documentType: v.optional(v.union(
      v.literal("research"),
      v.literal("screenplay"),
      v.literal("collection")
    )), // Optional for backward compatibility, defaults to "research"
    isPublic: v.optional(v.boolean()),
  })
    .index("by_created_by", ["createdBy"])
    .index("by_script", ["scriptId"])
    .index("by_script_and_type", ["scriptId", "documentType"]),
  
  scriptShares: defineTable({
    scriptId: v.id("scripts"),
    sharedWithUserId: v.id("users"),
    permission: v.union(v.literal("read"), v.literal("write")),
    sharedBy: v.id("users"),
  })
    .index("by_script", ["scriptId"])
    .index("by_user", ["sharedWithUserId"])
    .index("by_script_and_user", ["scriptId", "sharedWithUserId"]),

  documentShares: defineTable({
    documentId: v.id("documents"),
    sharedWithUserId: v.id("users"),
    permission: v.union(v.literal("read"), v.literal("write")),
    sharedBy: v.id("users"),
  })
    .index("by_document", ["documentId"])
    .index("by_user", ["sharedWithUserId"])
    .index("by_document_and_user", ["documentId", "sharedWithUserId"]),
    
  scriptInvitations: defineTable({
    scriptId: v.id("scripts"),
    invitedEmail: v.string(),
    permission: v.union(v.literal("read"), v.literal("write")),
    invitedBy: v.id("users"),
    token: v.string(),
    expiresAt: v.number(),
    acceptedAt: v.optional(v.number()),
    acceptedBy: v.optional(v.id("users")),
  })
    .index("by_script", ["scriptId"])
    .index("by_token", ["token"])
    .index("by_email", ["invitedEmail"]),

  documentInvitations: defineTable({
    documentId: v.id("documents"),
    invitedEmail: v.string(),
    permission: v.union(v.literal("read"), v.literal("write")),
    invitedBy: v.id("users"),
    token: v.string(),
    expiresAt: v.number(),
    acceptedAt: v.optional(v.number()),
    acceptedBy: v.optional(v.id("users")),
  })
    .index("by_document", ["documentId"])
    .index("by_token", ["token"])
    .index("by_email", ["invitedEmail"]),

  scriptPermissionRequests: defineTable({
    scriptId: v.id("scripts"),
    requestedBy: v.id("users"),
    currentPermission: v.union(v.literal("read"), v.literal("write")),
    requestedPermission: v.union(v.literal("read"), v.literal("write")),
    status: v.union(v.literal("pending"), v.literal("approved"), v.literal("denied")),
    message: v.optional(v.string()),
    reviewedBy: v.optional(v.id("users")),
    reviewedAt: v.optional(v.number()),
  })
    .index("by_script", ["scriptId"])
    .index("by_user", ["requestedBy"])
    .index("by_script_and_user", ["scriptId", "requestedBy"])
    .index("by_status", ["status"]),

  permissionRequests: defineTable({
    documentId: v.id("documents"),
    requestedBy: v.id("users"),
    currentPermission: v.union(v.literal("read"), v.literal("write")),
    requestedPermission: v.union(v.literal("read"), v.literal("write")),
    status: v.union(v.literal("pending"), v.literal("approved"), v.literal("denied")),
    message: v.optional(v.string()),
    reviewedBy: v.optional(v.id("users")),
    reviewedAt: v.optional(v.number()),
  })
    .index("by_document", ["documentId"])
    .index("by_user", ["requestedBy"])
    .index("by_document_and_user", ["documentId", "requestedBy"])
    .index("by_status", ["status"]),

  cursors: defineTable({
    documentId: v.id("documents"),
    userId: v.id("users"),
    position: v.number(),
    selection: v.optional(v.object({
      from: v.number(),
      to: v.number(),
    })),
    timestamp: v.number(),
    lastActive: v.number(),
  })
    .index("by_document", ["documentId"])
    .index("by_document_user", ["documentId", "userId"])
    .index("by_last_active", ["lastActive"]),

  comments: defineTable({
    documentId: v.id("documents"),
    userId: v.id("users"),
    content: v.string(),
    // Position and selection data for anchoring comment to text
    position: v.number(),
    selection: v.object({
      from: v.number(),
      to: v.number(),
    }),
    // Original text that was commented on (for reference)
    selectedText: v.string(),
    isResolved: v.optional(v.boolean()),
    timestamp: v.number(),
  })
    .index("by_document", ["documentId"])
    .index("by_document_user", ["documentId", "userId"])
    .index("by_timestamp", ["timestamp"]),

  commentReplies: defineTable({
    commentId: v.id("comments"),
    userId: v.id("users"),
    content: v.string(),
    timestamp: v.number(),
  })
    .index("by_comment", ["commentId"])
    .index("by_comment_user", ["commentId", "userId"])
    .index("by_timestamp", ["timestamp"]),
};

export default defineSchema({
  ...authTables,
  ...applicationTables,
});

import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const shareDocument = mutation({
  args: {
    documentId: v.id("documents"),
    userEmail: v.string(),
    permission: v.union(v.literal("read"), v.literal("write")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check if user owns the document
    const document = await ctx.db.get(args.documentId);
    if (!document || document.createdBy !== userId) {
      throw new Error("Access denied");
    }

    // Find user by email
    const targetUser = await ctx.db
      .query("users")
      .withIndex("email", (q) => q.eq("email", args.userEmail))
      .first();

    if (!targetUser) {
      throw new Error("User not found");
    }

    // Check if already shared
    const existingShare = await ctx.db
      .query("documentShares")
      .withIndex("by_document_and_user", (q) =>
        q.eq("documentId", args.documentId).eq("sharedWithUserId", targetUser._id)
      )
      .first();

    if (existingShare) {
      // Update existing share
      await ctx.db.patch(existingShare._id, {
        permission: args.permission,
      });
    } else {
      // Create new share
      await ctx.db.insert("documentShares", {
        documentId: args.documentId,
        sharedWithUserId: targetUser._id,
        permission: args.permission,
        sharedBy: userId,
      });
    }

    return { success: true };
  },
});

export const removeShare = mutation({
  args: {
    documentId: v.id("documents"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const currentUserId = await getAuthUserId(ctx);
    if (!currentUserId) {
      throw new Error("Not authenticated");
    }

    // Check if user owns the document
    const document = await ctx.db.get(args.documentId);
    if (!document || document.createdBy !== currentUserId) {
      throw new Error("Access denied");
    }

    const share = await ctx.db
      .query("documentShares")
      .withIndex("by_document_and_user", (q) =>
        q.eq("documentId", args.documentId).eq("sharedWithUserId", args.userId)
      )
      .first();

    if (share) {
      await ctx.db.delete(share._id);
    }

    return { success: true };
  },
});

export const getDocumentShares = query({
  args: { documentId: v.id("documents") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    // Check if user owns the document
    const document = await ctx.db.get(args.documentId);
    if (!document || document.createdBy !== userId) {
      return [];
    }

    const shares = await ctx.db
      .query("documentShares")
      .withIndex("by_document", (q) => q.eq("documentId", args.documentId))
      .collect();

    const sharesWithUserInfo = await Promise.all(
      shares.map(async (share) => {
        const user = await ctx.db.get(share.sharedWithUserId);
        return {
          ...share,
          user: user ? {
            _id: user._id,
            name: user.name,
            email: user.email
          } : null,
        };
      })
    );

    return sharesWithUserInfo.filter(share => share.user);
  },
});

export const getDocumentPermission = query({
  args: { documentId: v.id("documents") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const document = await ctx.db.get(args.documentId);
    if (!document) {
      return null;
    }

    // Get owner information
    const owner = await ctx.db.get(document.createdBy);

    // Owner has full access
    if (document.createdBy === userId) {
      return {
        permission: "owner",
        canShare: true,
        owner: owner ? {
          _id: owner._id,
          name: owner.name,
          email: owner.email
        } : null
      };
    }

    // Check shared access
    const share = await ctx.db
      .query("documentShares")
      .withIndex("by_document_and_user", (q) =>
        q.eq("documentId", args.documentId).eq("sharedWithUserId", userId)
      )
      .first();

    if (share) {
      return {
        permission: share.permission,
        canShare: false,
        owner: owner ? {
          _id: owner._id,
          name: owner.name,
          email: owner.email
        } : null
      };
    }

    return null;
  },
});

export const requestPermissionUpgrade = mutation({
  args: {
    documentId: v.id("documents"),
    message: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check if user has access to the document
    const share = await ctx.db
      .query("documentShares")
      .withIndex("by_document_and_user", (q) =>
        q.eq("documentId", args.documentId).eq("sharedWithUserId", userId)
      )
      .first();

    if (!share || share.permission !== "read") {
      throw new Error("Can only request upgrade from read permission");
    }

    // Check if there's already a pending request
    const existingRequest = await ctx.db
      .query("permissionRequests")
      .withIndex("by_document_and_user", (q) =>
        q.eq("documentId", args.documentId).eq("requestedBy", userId)
      )
      .filter((q) => q.eq(q.field("status"), "pending"))
      .first();

    if (existingRequest) {
      throw new Error("You already have a pending request for this document");
    }

    // Create permission request
    await ctx.db.insert("permissionRequests", {
      documentId: args.documentId,
      requestedBy: userId,
      currentPermission: "read",
      requestedPermission: "write",
      status: "pending",
      message: args.message,
    });

    return { success: true };
  },
});

export const getPendingPermissionRequests = query({
  args: { documentId: v.id("documents") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    // Check if user owns the document
    const document = await ctx.db.get(args.documentId);
    if (!document || document.createdBy !== userId) {
      return [];
    }

    const requests = await ctx.db
      .query("permissionRequests")
      .withIndex("by_document", (q) => q.eq("documentId", args.documentId))
      .filter((q) => q.eq(q.field("status"), "pending"))
      .collect();

    const requestsWithUserInfo = await Promise.all(
      requests.map(async (request) => {
        const user = await ctx.db.get(request.requestedBy);
        return {
          ...request,
          user: user ? {
            _id: user._id,
            name: user.name,
            email: user.email
          } : null,
        };
      })
    );

    return requestsWithUserInfo.filter(request => request.user);
  },
});

export const getUserPermissionRequest = query({
  args: { documentId: v.id("documents") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const request = await ctx.db
      .query("permissionRequests")
      .withIndex("by_document_and_user", (q) =>
        q.eq("documentId", args.documentId).eq("requestedBy", userId)
      )
      .filter((q) => q.eq(q.field("status"), "pending"))
      .first();

    return request;
  },
});

export const reviewPermissionRequest = mutation({
  args: {
    requestId: v.id("permissionRequests"),
    action: v.union(v.literal("approve"), v.literal("deny")),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const request = await ctx.db.get(args.requestId);
    if (!request) {
      throw new Error("Request not found");
    }

    // Check if user owns the document
    const document = await ctx.db.get(request.documentId);
    if (!document || document.createdBy !== userId) {
      throw new Error("Access denied");
    }

    // Update request status
    await ctx.db.patch(args.requestId, {
      status: args.action === "approve" ? "approved" : "denied",
      reviewedBy: userId,
      reviewedAt: Date.now(),
    });

    // If approved, update the user's permission
    if (args.action === "approve") {
      const share = await ctx.db
        .query("documentShares")
        .withIndex("by_document_and_user", (q) =>
          q.eq("documentId", request.documentId).eq("sharedWithUserId", request.requestedBy)
        )
        .first();

      if (share) {
        await ctx.db.patch(share._id, {
          permission: "write",
        });
      }
    }

    return { success: true };
  },
});

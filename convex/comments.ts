import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Id } from "./_generated/dataModel";
import { api } from "./_generated/api";

export const createComment = mutation({
  args: {
    documentId: v.id("documents"),
    content: v.string(),
    position: v.number(),
    selection: v.object({
      from: v.number(),
      to: v.number(),
    }),
    selectedText: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check if user has access to this document
    const permission = await ctx.runQuery(api.sharing.getDocumentPermission, { 
      documentId: args.documentId 
    });
    
    if (!permission) {
      throw new Error("Access denied");
    }

    const commentId = await ctx.db.insert("comments", {
      documentId: args.documentId,
      userId,
      content: args.content,
      position: args.position,
      selection: args.selection,
      selectedText: args.selectedText,
      isResolved: false,
      timestamp: Date.now(),
    });

    return commentId;
  },
});

export const getDocumentComments = query({
  args: { documentId: v.id("documents") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    // Check if user has access to this document
    const permission = await ctx.runQuery(api.sharing.getDocumentPermission, { 
      documentId: args.documentId 
    });
    
    if (!permission) {
      return [];
    }

    const comments = await ctx.db
      .query("comments")
      .withIndex("by_document", (q) => q.eq("documentId", args.documentId))
      .order("desc")
      .collect();

    // Get user information for each comment
    const commentsWithUsers = await Promise.all(
      comments.map(async (comment) => {
        const user = await ctx.db.get(comment.userId);
        const replies = await ctx.db
          .query("commentReplies")
          .withIndex("by_comment", (q) => q.eq("commentId", comment._id))
          .order("asc")
          .collect();

        // Get user information for each reply
        const repliesWithUsers = await Promise.all(
          replies.map(async (reply) => {
            const replyUser = await ctx.db.get(reply.userId);
            return {
              ...reply,
              user: replyUser ? {
                _id: replyUser._id,
                name: replyUser.name,
                email: replyUser.email,
                image: replyUser.image,
              } : null,
            };
          })
        );

        return {
          ...comment,
          user: user ? {
            _id: user._id,
            name: user.name,
            email: user.email,
            image: user.image,
          } : null,
          replies: repliesWithUsers,
        };
      })
    );

    return commentsWithUsers;
  },
});

export const updateComment = mutation({
  args: {
    commentId: v.id("comments"),
    content: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new Error("Comment not found");
    }

    // Only the comment author can edit their comment
    if (comment.userId !== userId) {
      throw new Error("Permission denied");
    }

    await ctx.db.patch(args.commentId, {
      content: args.content,
      timestamp: Date.now(), // Update timestamp to show it was edited
    });

    return args.commentId;
  },
});

export const deleteComment = mutation({
  args: {
    commentId: v.id("comments"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new Error("Comment not found");
    }

    // Only the comment author can delete their comment
    if (comment.userId !== userId) {
      throw new Error("Permission denied");
    }

    // Delete all replies first
    const replies = await ctx.db
      .query("commentReplies")
      .withIndex("by_comment", (q) => q.eq("commentId", args.commentId))
      .collect();

    for (const reply of replies) {
      await ctx.db.delete(reply._id);
    }

    // Delete the comment
    await ctx.db.delete(args.commentId);

    return args.commentId;
  },
});

export const createReply = mutation({
  args: {
    commentId: v.id("comments"),
    content: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new Error("Comment not found");
    }

    // Check if user has access to the document
    const permission = await ctx.runQuery(api.sharing.getDocumentPermission, { 
      documentId: comment.documentId 
    });
    
    if (!permission) {
      throw new Error("Access denied");
    }

    const replyId = await ctx.db.insert("commentReplies", {
      commentId: args.commentId,
      userId,
      content: args.content,
      timestamp: Date.now(),
    });

    return replyId;
  },
});

export const updateReply = mutation({
  args: {
    replyId: v.id("commentReplies"),
    content: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const reply = await ctx.db.get(args.replyId);
    if (!reply) {
      throw new Error("Reply not found");
    }

    // Only the reply author can edit their reply
    if (reply.userId !== userId) {
      throw new Error("Permission denied");
    }

    await ctx.db.patch(args.replyId, {
      content: args.content,
      timestamp: Date.now(), // Update timestamp to show it was edited
    });

    return args.replyId;
  },
});

export const deleteReply = mutation({
  args: {
    replyId: v.id("commentReplies"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const reply = await ctx.db.get(args.replyId);
    if (!reply) {
      throw new Error("Reply not found");
    }

    // Only the reply author can delete their reply
    if (reply.userId !== userId) {
      throw new Error("Permission denied");
    }

    await ctx.db.delete(args.replyId);

    return args.replyId;
  },
});

export const resolveComment = mutation({
  args: {
    commentId: v.id("comments"),
    isResolved: v.boolean(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new Error("Comment not found");
    }

    // Check if user has write access to the document
    const permission = await ctx.runQuery(api.sharing.getDocumentPermission, { 
      documentId: comment.documentId 
    });
    
    if (!permission || permission.permission === "read") {
      throw new Error("Write access required");
    }

    await ctx.db.patch(args.commentId, {
      isResolved: args.isResolved,
    });

    return args.commentId;
  },
});

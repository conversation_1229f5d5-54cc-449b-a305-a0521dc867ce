# Test info

- Name: Side Menu Content Node Tools >> should display side menu tools when hovering over content blocks
- Location: /Users/<USER>/Developer/collaborative_content_editor_ko8hwk/src/__tests__/integration/side-menu.integration.test.ts:18:3

# Error details

```
Error: page.click: Test ended.
Call log:
  - waiting for locator('button:has-text("New Document")')

    at /Users/<USER>/Developer/collaborative_content_editor_ko8hwk/src/__tests__/integration/side-menu.integration.test.ts:14:16
```

# Test source

```ts
   1 | /**
   2 |  * Integration tests for BlockNote side menu functionality
   3 |  * Tests the content node context selection tools (plus button, drag handle, etc.)
   4 |  */
   5 |
   6 | import { test, expect } from '@playwright/test';
   7 |
   8 | test.describe('Side Menu Content Node Tools', () => {
   9 |   test.beforeEach(async ({ page }) => {
   10 |     // Navigate to the application
   11 |     await page.goto('/');
   12 |     
   13 |     // Create a new document to test with
>  14 |     await page.click('button:has-text("New Document")');
      |                ^ Error: page.click: Test ended.
   15 |     await page.waitForSelector('.prose', { timeout: 10000 });
   16 |   });
   17 |
   18 |   test('should display side menu tools when hovering over content blocks', async ({ page }) => {
   19 |     const editor = page.locator('.prose');
   20 |     await editor.click();
   21 |     
   22 |     // Add some content to create a block
   23 |     await page.keyboard.type('This is a test paragraph for side menu functionality.');
   24 |     await page.waitForTimeout(500);
   25 |     
   26 |     // Look for the side menu elements that should appear on hover
   27 |     // BlockNote typically shows side menu on the left side of blocks
   28 |     const sideMenuElements = page.locator('[data-testid*="side-menu"], .bn-side-menu, [class*="side-menu"]');
   29 |     
   30 |     // Check if any side menu related elements are present
   31 |     const sideMenuCount = await sideMenuElements.count();
   32 |     console.log(`Found ${sideMenuCount} side menu elements`);
   33 |     
   34 |     // If we don't find specific test IDs, look for common BlockNote side menu patterns
   35 |     const addButton = page.locator('button:has-text("+"), [aria-label*="Add"], [title*="Add"]');
   36 |     const dragHandle = page.locator('[aria-label*="Drag"], [title*="Drag"], button:has-text("⠿")');
   37 |     
   38 |     // Check if add button exists
   39 |     const addButtonCount = await addButton.count();
   40 |     console.log(`Found ${addButtonCount} add buttons`);
   41 |     
   42 |     // Check if drag handle exists  
   43 |     const dragHandleCount = await dragHandle.count();
   44 |     console.log(`Found ${dragHandleCount} drag handles`);
   45 |     
   46 |     // At least one of these should be present for a functioning side menu
   47 |     expect(sideMenuCount + addButtonCount + dragHandleCount).toBeGreaterThan(0);
   48 |   });
   49 |
   50 |   test('should show plus button for adding new blocks', async ({ page }) => {
   51 |     const editor = page.locator('.prose');
   52 |     await editor.click();
   53 |     
   54 |     // Add content
   55 |     await page.keyboard.type('Test content for plus button functionality.');
   56 |     await page.waitForTimeout(500);
   57 |     
   58 |     // Look for plus button or add block functionality
   59 |     const plusButtons = page.locator('button:has-text("+"), [aria-label*="Add block"], [title*="Add block"]');
   60 |     
   61 |     // Check if we can find any plus/add buttons
   62 |     const count = await plusButtons.count();
   63 |     console.log(`Found ${count} plus/add buttons`);
   64 |     
   65 |     if (count > 0) {
   66 |       // Try to click the first plus button
   67 |       await plusButtons.first().click();
   68 |       await page.waitForTimeout(500);
   69 |       
   70 |       // Check if a menu or options appeared
   71 |       const menus = page.locator('[role="menu"], .dropdown, [class*="menu"]');
   72 |       const menuCount = await menus.count();
   73 |       console.log(`Found ${menuCount} menus after clicking plus button`);
   74 |     }
   75 |     
   76 |     // The test passes if we found at least one plus button
   77 |     expect(count).toBeGreaterThan(0);
   78 |   });
   79 |
   80 |   test('should show drag handle for block manipulation', async ({ page }) => {
   81 |     const editor = page.locator('.prose');
   82 |     await editor.click();
   83 |     
   84 |     // Add content
   85 |     await page.keyboard.type('Test content for drag handle functionality.');
   86 |     await page.waitForTimeout(500);
   87 |     
   88 |     // Look for drag handle (often represented by ⠿ or similar)
   89 |     const dragHandles = page.locator('button:has-text("⠿"), [aria-label*="Drag"], [title*="Drag"], [class*="drag"]');
   90 |     
   91 |     const count = await dragHandles.count();
   92 |     console.log(`Found ${count} drag handles`);
   93 |     
   94 |     if (count > 0) {
   95 |       // Try to click the drag handle to see if it opens a menu
   96 |       await dragHandles.first().click();
   97 |       await page.waitForTimeout(500);
   98 |       
   99 |       // Look for context menu options
  100 |       const contextMenus = page.locator('[role="menu"], .context-menu, [class*="menu"]');
  101 |       const menuCount = await contextMenus.count();
  102 |       console.log(`Found ${menuCount} context menus after clicking drag handle`);
  103 |     }
  104 |     
  105 |     // The test passes if we found at least one drag handle
  106 |     expect(count).toBeGreaterThan(0);
  107 |   });
  108 |
  109 |   test('should provide block manipulation options', async ({ page }) => {
  110 |     const editor = page.locator('.prose');
  111 |     await editor.click();
  112 |     
  113 |     // Add content
  114 |     await page.keyboard.type('Test content for block manipulation options.');
```
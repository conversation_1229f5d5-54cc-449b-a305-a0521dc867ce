# Test info

- Name: ScreenplayEditor Simple Tests >> should show application loaded correctly
- Location: /Users/<USER>/Developer/collaborative_content_editor_ko8hwk/src/__tests__/integration/screenplay-editor-simple.test.ts:17:3

# Error details

```
Error: expect.toBeVisible: Error: strict mode violation: locator('h2:has-text("Scripty")') resolved to 2 elements:
    1) <h2 class="text-xl font-semibold text-gray-900">Scripty</h2> aka getByRole('heading', { name: '<PERSON>ripty', exact: true })
    2) <h2 class="text-2xl font-semibold mb-3 text-gray-900">Welcome to Scripty</h2> aka getByRole('heading', { name: 'Welcome to Scripty' })

Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('h2:has-text("Scripty")')

    at /Users/<USER>/Developer/collaborative_content_editor_ko8hwk/src/__tests__/integration/screenplay-editor-simple.test.ts:19:58
```

# Page snapshot

```yaml
- banner:
  - heading "Scripty" [level=2]
  - button "Sign out"
- main:
  - heading "My Scripts" [level=1]
  - paragraph: Limited Access
  - paragraph: Anonymous users can only view shared scripts.
  - paragraph: No scripts available
  - heading "My Documents" [level=1]
  - paragraph: Limited Access
  - paragraph: Anonymous users can only view shared documents.
  - heading "No documents yet" [level=3]
  - paragraph: Sign up for an account or ask someone to share a document with you.
  - heading "Welcome to Scripty" [level=2]
  - paragraph: Create your first script or document to get started
  - text: Real-time collaboration Rich text editing Screenplay formatting
- region "Notifications alt+T"
```

# Test source

```ts
   1 | /**
   2 |  * Simple Integration Test for ScreenplayEditor Component
   3 |  * Tests basic functionality without complex setup
   4 |  */
   5 |
   6 | import { test, expect } from '@playwright/test';
   7 |
   8 | test.describe('ScreenplayEditor Simple Tests', () => {
   9 |   test.beforeEach(async ({ page }) => {
   10 |     await page.goto('/');
   11 |     
   12 |     // Sign in anonymously to access editor features
   13 |     await page.click('button:has-text("Sign in anonymously")');
   14 |     await expect(page.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });
   15 |   });
   16 |
   17 |   test('should show application loaded correctly', async ({ page }) => {
   18 |     // Check if the main application elements are present
>  19 |     await expect(page.locator('h2:has-text("Scripty")')).toBeVisible();
      |                                                          ^ Error: expect.toBeVisible: Error: strict mode violation: locator('h2:has-text("Scripty")') resolved to 2 elements:
   20 |     
   21 |     // Check if script list is present
   22 |     await expect(page.locator('text=My Scripts')).toBeVisible();
   23 |     
   24 |     // Check if we can see the New Script button (if user has permissions)
   25 |     const newScriptButton = page.locator('button:has-text("New Script")');
   26 |     const isVisible = await newScriptButton.isVisible();
   27 |     console.log('New Script button visible:', isVisible);
   28 |     
   29 |     if (isVisible) {
   30 |       console.log('✅ User has script creation permissions');
   31 |     } else {
   32 |       console.log('ℹ️ User does not have script creation permissions');
   33 |     }
   34 |   });
   35 |
   36 |   test('should handle direct navigation to invalid screenplay URL', async ({ page }) => {
   37 |     // Navigate directly to an invalid screenplay URL
   38 |     await page.goto('/script/invalid-script-id/document/invalid-document-id');
   39 |     
   40 |     // Should not crash the application
   41 |     await page.waitForTimeout(3000);
   42 |     
   43 |     // Check that we don't have any unhandled JavaScript errors
   44 |     const errors = [];
   45 |     page.on('pageerror', error => errors.push(error));
   46 |     
   47 |     await page.waitForTimeout(2000);
   48 |     
   49 |     // Should not have any unhandled JavaScript errors
   50 |     expect(errors.length).toBe(0);
   51 |     
   52 |     // Should show some kind of error or loading state
   53 |     const body = await page.locator('body').textContent();
   54 |     expect(body).toBeTruthy();
   55 |   });
   56 |
   57 |   test('should inject screenplay CSS when ScreenplayEditor is imported', async ({ page }) => {
   58 |     // Even if we can't create a screenplay document, the CSS should be available
   59 |     // when the ScreenplayEditor component is loaded
   60 |     
   61 |     // Check if the ScreenplayEditor component exists in the bundle
   62 |     const hasScreenplayEditor = await page.evaluate(() => {
   63 |       // Check if the ScreenplayEditor is available in the module system
   64 |       return typeof window !== 'undefined';
   65 |     });
   66 |     
   67 |     expect(hasScreenplayEditor).toBe(true);
   68 |     
   69 |     // Navigate to a script URL pattern to potentially trigger ScreenplayEditor loading
   70 |     await page.goto('/script/test/document/test');
   71 |     await page.waitForTimeout(2000);
   72 |     
   73 |     // Check if screenplay CSS gets injected (it should be injected when component loads)
   74 |     const screenplayStyles = page.locator('#screenplay-styles');
   75 |     
   76 |     // The styles might be injected even if the document doesn't exist
   77 |     // because the component tries to load
   78 |     const stylesExist = await screenplayStyles.count() > 0;
   79 |     console.log('Screenplay styles injected:', stylesExist);
   80 |     
   81 |     if (stylesExist) {
   82 |       const stylesContent = await screenplayStyles.textContent();
   83 |       expect(stylesContent).toContain('screenplay-');
   84 |       console.log('✅ Screenplay CSS is working');
   85 |     } else {
   86 |       console.log('ℹ️ Screenplay CSS not injected (component may not have loaded)');
   87 |     }
   88 |   });
   89 |
   90 |   test('should handle component imports without errors', async ({ page }) => {
   91 |     // Test that all the screenplay-related components can be imported without errors
   92 |     const componentTest = await page.evaluate(() => {
   93 |       try {
   94 |         // This will test if the modules can be loaded without syntax errors
   95 |         return {
   96 |           success: true,
   97 |           error: null
   98 |         };
   99 |       } catch (error) {
  100 |         return {
  101 |           success: false,
  102 |           error: error.message
  103 |         };
  104 |       }
  105 |     });
  106 |     
  107 |     expect(componentTest.success).toBe(true);
  108 |     if (!componentTest.success) {
  109 |       console.error('Component import error:', componentTest.error);
  110 |     }
  111 |   });
  112 |
  113 |   test('should show proper error states for missing documents', async ({ page }) => {
  114 |     // Navigate to a script document URL that doesn't exist
  115 |     await page.goto('/script/nonexistent/document/nonexistent');
  116 |     
  117 |     // Wait for the page to load
  118 |     await page.waitForTimeout(3000);
  119 |     
```

<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">17.2% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>438/2546</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">61.6% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>69/112</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">32.75% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>19/58</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">17.2% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>438/2546</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="convex"><a href="convex/index.html">convex</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="717" class="abs low">0/717</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="10" class="abs low">0/10</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="10" class="abs low">0/10</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="717" class="abs low">0/717</td>
	</tr>

<tr>
	<td class="file low" data-value="src"><a href="src/index.html">src</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="172" class="abs low">0/172</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="172" class="abs low">0/172</td>
	</tr>

<tr>
	<td class="file low" data-value="src/components"><a href="src/components/index.html">src/components</a></td>
	<td data-value="18.85" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 18%"></div><div class="cover-empty" style="width: 82%"></div></div>
	</td>
	<td data-value="18.85" class="pct low">18.85%</td>
	<td data-value="923" class="abs low">174/923</td>
	<td data-value="55.31" class="pct medium">55.31%</td>
	<td data-value="47" class="abs medium">26/47</td>
	<td data-value="26.66" class="pct low">26.66%</td>
	<td data-value="15" class="abs low">4/15</td>
	<td data-value="18.85" class="pct low">18.85%</td>
	<td data-value="923" class="abs low">174/923</td>
	</tr>

<tr>
	<td class="file low" data-value="src/components/ui"><a href="src/components/ui/index.html">src/components/ui</a></td>
	<td data-value="27.43" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 27%"></div><div class="cover-empty" style="width: 73%"></div></div>
	</td>
	<td data-value="27.43" class="pct low">27.43%</td>
	<td data-value="492" class="abs low">135/492</td>
	<td data-value="38.46" class="pct low">38.46%</td>
	<td data-value="13" class="abs low">5/13</td>
	<td data-value="10" class="pct low">10%</td>
	<td data-value="10" class="abs low">1/10</td>
	<td data-value="27.43" class="pct low">27.43%</td>
	<td data-value="492" class="abs low">135/492</td>
	</tr>

<tr>
	<td class="file low" data-value="src/hooks"><a href="src/hooks/index.html">src/hooks</a></td>
	<td data-value="38.33" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 38%"></div><div class="cover-empty" style="width: 62%"></div></div>
	</td>
	<td data-value="38.33" class="pct low">38.33%</td>
	<td data-value="180" class="abs low">69/180</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="12" class="abs high">12/12</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="9" class="abs low">3/9</td>
	<td data-value="38.33" class="pct low">38.33%</td>
	<td data-value="180" class="abs low">69/180</td>
	</tr>

<tr>
	<td class="file high" data-value="src/lib"><a href="src/lib/index.html">src/lib</a></td>
	<td data-value="96.77" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 96%"></div><div class="cover-empty" style="width: 4%"></div></div>
	</td>
	<td data-value="96.77" class="pct high">96.77%</td>
	<td data-value="62" class="abs high">60/62</td>
	<td data-value="96.29" class="pct high">96.29%</td>
	<td data-value="27" class="abs high">26/27</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	<td data-value="96.77" class="pct high">96.77%</td>
	<td data-value="62" class="abs high">60/62</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-07T08:18:13.337Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    